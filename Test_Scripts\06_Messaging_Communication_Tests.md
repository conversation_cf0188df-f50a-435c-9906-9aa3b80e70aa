# Test Script 06: Messaging & Communication Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Mobile (375x667)
- **Test Accounts**: Free user, Finder subscriber, Achiever subscriber
- **Additional**: Property owner account for testing (if available)
- **Test URL**: https://propertyplaza.com

---

## TS06-01: Owner Contact Functionality Test

### Objective
Test the owner contact feature with subscription-based limitations and chat initiation.

### Pre-conditions
- Property detail page loaded
- Different user account types available for testing

### Test Steps
1. **Unauthenticated User Contact Attempt**:
   - Logout from any account
   - Navigate to property detail page
   - Click "Contact Owner" button
   - Verify login prompt appears
   - Check login prompt messaging
   - Complete login and retry contact

2. **Free User Contact Attempt**:
   - Login as free user
   - Navigate to property detail page
   - Click "Contact Owner" button
   - Verify upgrade prompt appears instead of chat
   - Check upgrade messaging mentions contact feature
   - Verify upgrade button leads to subscription page

3. **Subscribed User Contact (Finder)**:
   - Login as Finder subscriber
   - Navigate to property detail page
   - Click "Contact Owner" button
   - Verify chat window opens
   - Check chat interface loads properly
   - Send test message to owner
   - Verify message sends successfully

4. **Contact Limit Tracking (Finder - 5/week)**:
   - Contact 5 different property owners within a week
   - Verify contact counter decreases with each contact
   - Try to contact 6th owner
   - Verify limit reached message appears
   - Check upgrade prompt for higher limits

5. **Subscribed User Contact (Achiever)**:
   - Login as Achiever subscriber
   - Test contact functionality
   - Verify higher weekly limit (15 contacts)
   - Test contact counter accuracy

### Expected Results
- Proper authentication checks for contact feature
- Free users see upgrade prompts with clear messaging
- Subscribed users can initiate chats successfully
- Contact limits enforced accurately per subscription tier
- Contact counters display correctly

### Pass/Fail Criteria
- **PASS**: Contact feature works correctly with proper restrictions
- **FAIL**: Incorrect access control, limits not enforced, or chat doesn't work

---

## TS06-02: Chat Interface Functionality Test

### Objective
Test the chat interface functionality, message sending/receiving, and user experience.

### Pre-conditions
- Subscribed user account (Finder or Achiever)
- Active chat with property owner initiated

### Test Steps
1. **Chat Interface Layout**:
   - Verify chat window displays properly
   - Check message history area
   - Verify message input field
   - Check send button functionality
   - Verify chat header with property/owner info

2. **Message Sending**:
   - Type test message: "Hello, I'm interested in this property"
   - Click send button
   - Verify message appears in chat history
   - Check message timestamp
   - Verify message status indicators (sent/delivered)

3. **Message Formatting**:
   - Send message with emojis
   - Send longer message (multiple lines)
   - Test special characters
   - Verify proper message formatting and display

4. **Chat History**:
   - Send multiple messages
   - Close chat window
   - Reopen chat
   - Verify message history persists
   - Check chronological order of messages

5. **Real-time Functionality**:
   - Keep chat open
   - If possible, have owner respond (or simulate)
   - Verify new messages appear without refresh
   - Check notification indicators for new messages

6. **Chat Management**:
   - Test chat minimize/maximize
   - Test chat close functionality
   - Verify chat reopens with history intact

### Expected Results
- Clean, intuitive chat interface
- Messages send and display correctly
- Proper formatting and emoji support
- Chat history persists across sessions
- Real-time message updates
- Good overall user experience

### Pass/Fail Criteria
- **PASS**: Chat interface works smoothly with all features functional
- **FAIL**: Messages don't send, interface issues, or poor user experience

---

## TS06-03: Customer Support Chat Test

### Objective
Test customer support chat functionality available to all users.

### Pre-conditions
- Any user account (Free, Finder, or Achiever)
- Customer support feature available

### Test Steps
1. **Support Chat Access**:
   - Locate customer support chat option (help icon, support menu, etc.)
   - Click on customer support chat
   - Verify support chat window opens
   - Check if available for all user types

2. **Support Chat Interface**:
   - Verify support chat has different styling/branding
   - Check if support agent information displays
   - Verify chat interface is user-friendly
   - Test message input functionality

3. **Support Message Sending**:
   - Send test support message: "I need help with my account"
   - Verify message sends successfully
   - Check for automated responses or acknowledgments
   - Verify message appears in chat history

4. **Support Chat Features**:
   - Test file attachment if available
   - Test screenshot sharing if available
   - Verify chat history persistence
   - Test support chat notifications

5. **Support Availability**:
   - Check support hours display
   - Test behavior during off-hours
   - Verify queue/wait time information if applicable
   - Check automated responses for common questions

### Expected Results
- Support chat accessible to all users
- Clear distinction from owner chat
- Professional support interface
- Messages send successfully
- Appropriate automated responses
- Clear support availability information

### Pass/Fail Criteria
- **PASS**: Support chat works well for all user types
- **FAIL**: Support chat inaccessible, doesn't work, or poor experience

---

## TS06-04: Chat List and Management Test

### Objective
Test the chat list overview and chat management functionality.

### Pre-conditions
- Subscribed user with multiple active chats
- Chat history with different property owners

### Test Steps
1. **Chat List Access**:
   - Navigate to messages/chat section
   - Verify chat list displays all active conversations
   - Check chat list layout and organization

2. **Chat List Information**:
   - Verify each chat shows:
     - Property owner name/info
     - Property title or image
     - Last message preview
     - Timestamp of last message
     - Unread message indicators

3. **Chat List Sorting**:
   - Verify chats are sorted by recent activity
   - Send new message in older chat
   - Verify chat moves to top of list
   - Check sorting accuracy

4. **Chat List Search**:
   - Use search functionality to find specific chat
   - Search by property name
   - Search by owner name
   - Verify search results accuracy

5. **Chat Management Actions**:
   - Test chat deletion if available
   - Test chat archiving if available
   - Test mark as read/unread functionality
   - Verify actions work correctly

6. **Unread Message Handling**:
   - Verify unread message counters
   - Check unread indicators in chat list
   - Test mark all as read functionality

### Expected Results
- Comprehensive chat list with all conversations
- Clear information display for each chat
- Proper sorting by recent activity
- Functional search capability
- Effective chat management options
- Accurate unread message tracking

### Pass/Fail Criteria
- **PASS**: Chat list and management work effectively
- **FAIL**: Missing chats, poor organization, or management features don't work

---

## TS06-05: Notification Settings Test

### Objective
Test notification preferences and delivery across different channels.

### Pre-conditions
- User account with notification settings access
- Email account accessible for testing

### Test Steps
1. **Notification Settings Access**:
   - Navigate to notification settings
   - Verify all notification types are listed:
     - New messages
     - Property updates
     - Price alerts
     - Newsletter
     - Marketing communications

2. **Email Notification Settings**:
   - Enable email notifications for new messages
   - Send test message (or have one sent)
   - Check email inbox for notification
   - Verify email content and formatting
   - Test disable email notifications

3. **Push Notification Settings**:
   - Enable browser push notifications
   - Grant permission when prompted
   - Send test message to trigger notification
   - Verify push notification appears
   - Test notification click behavior

4. **Sound Notification Settings**:
   - Enable sound notifications
   - Adjust volume settings if available
   - Test sound notification on new message
   - Verify sound plays appropriately
   - Test disable sound notifications

5. **Notification Frequency Settings**:
   - Test immediate notification settings
   - Test digest/summary notification options
   - Configure notification frequency preferences
   - Verify settings save correctly

6. **Notification Preferences by Type**:
   - Configure different settings for different notification types
   - Test granular control over notifications
   - Verify each type respects individual settings

### Expected Results
- Comprehensive notification settings available
- Email notifications work reliably
- Push notifications function correctly
- Sound notifications work appropriately
- Granular control over notification types
- Settings persist across sessions

### Pass/Fail Criteria
- **PASS**: All notification types work with proper user control
- **FAIL**: Notifications don't work, settings don't save, or poor control

---

## TS06-06: Property Alert Notifications Test

### Objective
Test property alert functionality for new listings and price changes.

### Pre-conditions
- User account with saved searches or favorite properties
- New properties being added to system (or ability to simulate)

### Test Steps
1. **Property Alert Setup**:
   - Create saved search with specific criteria
   - Enable alerts for new properties matching criteria
   - Configure alert frequency (immediate, daily, weekly)
   - Save alert preferences

2. **New Property Alert Test**:
   - Add new property matching saved search criteria (or simulate)
   - Verify alert notification is triggered
   - Check email notification for new property
   - Verify notification contains property details
   - Test notification link to property

3. **Price Alert Setup**:
   - Add property to favorites
   - Enable price alerts for favorited property
   - Set price change threshold if available
   - Save price alert settings

4. **Price Change Alert Test**:
   - Change property price (or simulate price change)
   - Verify price alert notification triggers
   - Check email notification for price change
   - Verify notification shows old and new prices
   - Test notification link functionality

5. **Alert Management**:
   - View all active alerts
   - Edit alert criteria
   - Disable specific alerts
   - Delete alerts
   - Verify alert management works correctly

6. **Alert Frequency Testing**:
   - Test immediate alerts
   - Test daily digest alerts
   - Test weekly summary alerts
   - Verify frequency settings are respected

### Expected Results
- Easy alert setup for searches and properties
- Reliable alert delivery when criteria met
- Clear, informative alert notifications
- Functional links in alert emails
- Effective alert management interface
- Proper frequency control

### Pass/Fail Criteria
- **PASS**: Property alerts work reliably with good management
- **FAIL**: Alerts don't trigger, poor notification content, or management issues

---

## TS06-07: Mobile Communication Experience Test

### Objective
Test messaging and communication features on mobile devices.

### Pre-conditions
- Mobile device or mobile viewport (375px width)
- Subscribed user account with chat access

### Test Steps
1. **Mobile Chat Interface**:
   - Open chat on mobile device
   - Verify chat interface adapts to mobile screen
   - Check message input field usability
   - Test send button accessibility

2. **Mobile Chat List**:
   - Navigate to chat list on mobile
   - Verify list displays properly on small screen
   - Test chat selection and opening
   - Check swipe gestures if available

3. **Mobile Message Composition**:
   - Test typing messages with mobile keyboard
   - Verify autocorrect and predictive text work
   - Test emoji input on mobile
   - Check message formatting on mobile

4. **Mobile Notifications**:
   - Test push notifications on mobile device
   - Verify notification tap opens correct chat
   - Check notification display and content
   - Test notification sound on mobile

5. **Mobile Chat Performance**:
   - Test chat loading speed on mobile
   - Verify smooth scrolling through message history
   - Test real-time message updates on mobile
   - Check overall mobile chat performance

6. **Mobile Support Chat**:
   - Test customer support chat on mobile
   - Verify mobile-optimized support interface
   - Test support message sending on mobile
   - Check mobile support experience

### Expected Results
- Chat interface optimized for mobile use
- Easy message composition on mobile
- Proper notification handling on mobile
- Good performance on mobile devices
- Touch-friendly interface elements
- Consistent functionality across devices

### Pass/Fail Criteria
- **PASS**: Excellent mobile communication experience
- **FAIL**: Poor mobile optimization or functionality issues

---

## Test Completion Checklist

- [ ] TS06-01: Owner Contact Functionality Test
- [ ] TS06-02: Chat Interface Functionality Test
- [ ] TS06-03: Customer Support Chat Test
- [ ] TS06-04: Chat List and Management Test
- [ ] TS06-05: Notification Settings Test
- [ ] TS06-06: Property Alert Notifications Test
- [ ] TS06-07: Mobile Communication Experience Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 90-120 minutes
