# Test Script 03: Property Detail Page Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Tablet (768x1024), Mobile (375x667)
- **User Accounts**: Free user, Finder subscriber, Achiever subscriber
- **Test URL**: Property detail page from search results

---

## TS03-01: Property Information Display Test

### Objective
Verify that all property information displays correctly and completely.

### Pre-conditions
- Navigate to a property detail page from search results
- Property has complete information and multiple images

### Test Steps
1. **Property Title and Basic Info**:
   - Verify property title displays prominently
   - Check property type (Villa, Apartment, etc.)
   - Verify location information is accurate
   - Check availability status
2. **Price Display**:
   - Verify price shows in selected currency
   - Test currency switching on detail page
   - Check if price includes additional fees information
3. **Property Details**:
   - Verify bedrooms count with icon
   - Verify bathrooms count with icon
   - Check land size with proper units (sqm/are)
   - Check building size with proper units
   - Check garden size if applicable
4. **Property Description**:
   - Verify full description displays
   - Test "Read more" functionality if description is long
   - Check for proper text formatting
5. **Features and Amenities**:
   - Verify all features display with appropriate icons
   - Check categorization (Indoor, Outdoor, etc.)
   - Verify selling points are highlighted

### Expected Results
- All information displays clearly and accurately
- Proper formatting and typography throughout
- Icons and visual elements enhance readability
- Currency conversion works correctly
- No missing or truncated information

### Pass/Fail Criteria
- **PASS**: All property information displays correctly
- **FAIL**: Missing information, formatting issues, or display errors

---

## TS03-02: Image Gallery Functionality Test

### Objective
Test the property image gallery and all related functionality.

### Pre-conditions
- Property detail page loaded
- Property has multiple high-quality images

### Test Steps
1. **Gallery Display**:
   - Verify main image displays prominently
   - Check thumbnail gallery below main image
   - Verify image quality and proper aspect ratios
2. **Image Navigation**:
   - Click next arrow to advance through images
   - Click previous arrow to go back
   - Click on thumbnail images to jump to specific image
   - Test keyboard navigation (arrow keys)
3. **Image Zoom Functionality**:
   - Click on main image to zoom
   - Test zoom controls (+ and - buttons)
   - Test pan functionality when zoomed
   - Test double-click to zoom
4. **Fullscreen Mode**:
   - Click fullscreen button
   - Navigate through images in fullscreen
   - Test exit fullscreen (ESC key and close button)
5. **Lazy Loading**:
   - Observe image loading behavior
   - Check loading indicators
   - Verify performance with many images

### Expected Results
- Smooth navigation between images
- High-quality image display
- Zoom functionality works properly
- Fullscreen mode functions correctly
- Good performance with lazy loading

### Pass/Fail Criteria
- **PASS**: All gallery features work smoothly
- **FAIL**: Navigation issues, poor image quality, or broken functionality

---

## TS03-03: Map Functionality and Restrictions Test

### Objective
Test Google Maps integration and subscription-based zoom restrictions.

### Pre-conditions
- Property detail page loaded
- Test with different user account types (Free, Finder, Achiever)

### Test Steps
1. **Basic Map Display**:
   - Verify map loads with property location
   - Check if property pin is accurately placed
   - Verify map controls are visible and functional
2. **Map Navigation**:
   - Test pan/drag functionality
   - Test zoom in/out with controls
   - Test mouse wheel zoom
   - Test double-click zoom
3. **Zoom Restrictions by User Type**:
   
   **For Free Users**:
   - Zoom to maximum level
   - Verify zoom stops at level 13
   - Check upgrade banner appears
   - Verify upgrade banner content and CTA
   
   **For Finder Users**:
   - Zoom to maximum level
   - Verify zoom stops at level 14
   - Test if upgrade banner appears for Achiever
   
   **For Achiever Users**:
   - Zoom to maximum level
   - Verify zoom reaches level 15
   - Confirm no upgrade banners appear
4. **Map Performance**:
   - Test map loading speed
   - Verify smooth zoom and pan operations
   - Check map responsiveness

### Expected Results
- Map loads quickly with accurate location
- Zoom restrictions enforced based on subscription
- Upgrade banners appear at appropriate times
- Smooth map interactions
- Clear subscription benefit messaging

### Pass/Fail Criteria
- **PASS**: Map works correctly with proper restrictions
- **FAIL**: Incorrect zoom limits or non-functional map features

---

## TS03-04: Contact and Action Buttons Test

### Objective
Test all action buttons and their subscription-based restrictions.

### Pre-conditions
- Property detail page loaded
- Test with different user account types and authentication states

### Test Steps
1. **Contact Owner Button**:
   
   **Unauthenticated User**:
   - Click "Contact Owner" button
   - Verify login prompt appears
   - Complete login and retry contact
   
   **Free User (Authenticated)**:
   - Click "Contact Owner" button
   - Verify upgrade prompt appears
   - Check upgrade messaging and options
   
   **Subscribed User (Finder/Achiever)**:
   - Click "Contact Owner" button
   - Verify chat window opens
   - Test message sending functionality

2. **Add to Favorites**:
   
   **Unauthenticated User**:
   - Click heart/favorite icon
   - Verify login prompt appears
   
   **Free User**:
   - Click favorite icon
   - Verify upgrade prompt appears
   
   **Subscribed User**:
   - Click favorite icon
   - Verify property adds to favorites
   - Check visual feedback (filled heart)
   - Click again to remove from favorites

3. **Share Property**:
   - Click share button
   - Test social media sharing options
   - Test copy link functionality
   - Verify shared content includes proper metadata

4. **Save Property**:
   - Test save functionality (if different from favorites)
   - Verify saved properties list updates
   - Test remove from saved properties

### Expected Results
- Proper authentication checks for all actions
- Subscription restrictions enforced correctly
- Clear upgrade messaging for free users
- Functional contact and sharing features for subscribed users
- Visual feedback for all user interactions

### Pass/Fail Criteria
- **PASS**: All actions work correctly with proper restrictions
- **FAIL**: Incorrect access control or non-functional features

---

## TS03-05: Image Access Restrictions Test

### Objective
Test image access limitations for free users vs. subscribed users.

### Pre-conditions
- Property with 10+ images
- Test accounts: Free user and subscribed user

### Test Steps
1. **Free User Image Access**:
   - Login as free user
   - Navigate to property detail page
   - Count visible images in gallery
   - Verify only first 3 images are accessible
   - Check for upgrade prompt when trying to view more images
   - Verify upgrade messaging is clear and compelling

2. **Subscribed User Image Access**:
   - Login as Finder or Achiever user
   - Navigate to same property detail page
   - Verify all images are accessible
   - Test navigation through all images
   - Confirm no upgrade prompts appear

3. **Upgrade Flow from Image Restriction**:
   - As free user, click on upgrade prompt from image gallery
   - Verify navigation to subscription page
   - Check if subscription benefits are clearly explained
   - Test back navigation to property page

### Expected Results
- Free users see only first 3 images with clear upgrade messaging
- Subscribed users have access to all images
- Upgrade prompts are compelling and functional
- Smooth user experience for both user types

### Pass/Fail Criteria
- **PASS**: Image restrictions work correctly with clear upgrade path
- **FAIL**: Incorrect image access or poor upgrade messaging

---

## TS03-06: Property Inquiry Form Test

### Objective
Test the property inquiry form functionality and validation.

### Pre-conditions
- Property detail page loaded
- User authenticated (required for inquiries)

### Test Steps
1. **Form Access**:
   - Locate inquiry form or inquiry button
   - Verify form is accessible to authenticated users
   - Check form layout and fields

2. **Form Fields Validation**:
   - Test required field validation
   - Test email format validation
   - Test phone number format validation
   - Test message length limits
   - Test special character handling

3. **Form Submission**:
   - Fill out form with valid information
   - Submit inquiry
   - Verify success confirmation appears
   - Check if confirmation email is sent
   - Verify inquiry appears in user's message history

4. **Error Handling**:
   - Test form submission with invalid data
   - Verify appropriate error messages
   - Test network error handling
   - Test form reset functionality

### Expected Results
- Form is easily accessible and well-designed
- Proper validation with helpful error messages
- Successful submission with confirmation
- Inquiry reaches property owner
- Good error handling and user feedback

### Pass/Fail Criteria
- **PASS**: Form works correctly with proper validation
- **FAIL**: Form submission fails or poor validation

---

## TS03-07: Mobile Property Detail Experience Test

### Objective
Test property detail page functionality on mobile devices.

### Pre-conditions
- Mobile device or mobile viewport (375px width)
- Property detail page accessible

### Test Steps
1. **Mobile Layout**:
   - Verify property information displays properly
   - Check image gallery mobile optimization
   - Test map display and functionality on mobile
   - Verify all action buttons are touch-friendly

2. **Mobile Image Gallery**:
   - Test swipe navigation through images
   - Verify pinch-to-zoom functionality
   - Test fullscreen mode on mobile
   - Check image loading performance

3. **Mobile Map Interaction**:
   - Test touch-based map navigation
   - Verify zoom restrictions work on mobile
   - Test map performance on mobile device

4. **Mobile Actions**:
   - Test contact owner button on mobile
   - Test favorite functionality with touch
   - Test share functionality on mobile
   - Verify all buttons have appropriate touch targets

### Expected Results
- Optimized mobile layout with good usability
- Touch-friendly interactions throughout
- Good performance on mobile devices
- All functionality accessible on mobile

### Pass/Fail Criteria
- **PASS**: Excellent mobile experience with full functionality
- **FAIL**: Poor mobile optimization or inaccessible features

---

## Test Completion Checklist

- [ ] TS03-01: Property Information Display Test
- [ ] TS03-02: Image Gallery Functionality Test
- [ ] TS03-03: Map Functionality and Restrictions Test
- [ ] TS03-04: Contact and Action Buttons Test
- [ ] TS03-05: Image Access Restrictions Test
- [ ] TS03-06: Property Inquiry Form Test
- [ ] TS03-07: Mobile Property Detail Experience Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 75-90 minutes
