import type { Viewport } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import "./globals.css";
import { getMessages } from "next-intl/server";
import GoogleMapsProvider from "@/components/providers/google-maps-provider";
import TanstackQueryProvider from "@/components/providers/tanstack-query-provider";
import { Toaster } from "@/components/ui/toaster"
import NextTopLoader from "nextjs-toploader";
import MomentLocale from "@/components/locale/moment-locale";
import FacebookPixel from "./facebook-pixel";
import { Suspense } from "react";
import NotificationProvider from "@/components/providers/notification-provider";
import CookieConsent from "@/components/cookie-consent/cookie-consent";
import RecaptchaProvider from "@/components/providers/recaptcha-provider";
import Script from "next/script";

//give aditya autorisations

const inter = Inter({ subsets: ["latin"] });
export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
}

export default async function RootLayout({
  children, params
}: Readonly<{
  children: React.ReactNode, params: Promise<{ locale: string }>
}>) {
  const messages = await getMessages();
  const { locale } = await params
  return (
    <html lang={locale}>
      <meta name="google-site-verification" content="4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw" />
      <meta name="google-site-verification" content="T2LX_iF2IUPzRZQaIWMt0sDkp4nrs4W1W4UjsBcWJCA" />
      <NextIntlClientProvider messages={messages}>
        <RecaptchaProvider>
          <body className={`${inter.className} relative`}>
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NMNLWKBK"
              height="0" width="0" style={{ display: "none", visibility: "hidden" }}></iframe></noscript>
            <NotificationProvider />
            <Suspense fallback="null">
              <FacebookPixel />
            </Suspense>
            <MomentLocale />
            <NextTopLoader
              showSpinner={false}
            />
            <TanstackQueryProvider>
              <GoogleMapsProvider>
                {children}
                <Toaster />
              </GoogleMapsProvider>
            </TanstackQueryProvider>
            <CookieConsent />
          </body>

        </RecaptchaProvider>
      </NextIntlClientProvider>
      <Script
        id="Clarity"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `(function(c,l,a,r,i,t,y){
            c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "sjpyoxx18w");`,
        }}
      />

      {/* Google Tag Manager */}
      <Script
        id="GoogleTagManager"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-NMNLWKBK');`,
        }}
      />
    </html >
  );
}
