# Property Plaza - Comprehensive Seekers Test Plan

## 1. HOMEPAGE & NAVIGATION

### 1.1 Homepage Content Sections
- [ ] **Homepage Loading**: Verify homepage loads correctly with all sections visible
  - Test: Load homepage URL and verify all sections render within 3 seconds
  - Expected: Popular properties, newest properties, featured properties, categories, blog, FAQ, and "How it works" sections display
- [ ] **Popular Properties Section**: Verify popular properties display with correct data
  - Test: Check if popular properties show title, price, location, images, and selling points
  - Expected: Properties display with proper formatting, currency conversion, and clickable links
- [ ] **Newest Properties Section**: Verify newest properties section functionality
  - Test: Confirm newest properties load with lazy loading and show recent listings
  - Expected: Properties load on scroll with proper lazy loading indicators
- [ ] **Featured Properties Section**: Verify featured properties display correctly
  - Test: Check featured properties show with proper highlighting and premium indicators
  - Expected: Featured properties display with special styling and correct data
- [ ] **Category Content**: Verify property categories display and navigation
  - Test: Click on each category (Villa, Apartment, Rooms, Commercial, etc.) and verify navigation
  - Expected: Categories navigate to correct search pages with proper filters applied
- [ ] **Blog Content Section**: Verify blog articles display and functionality
  - Test: Check blog articles load dynamically and link to detail pages
  - Expected: Blog articles display with images, titles, excerpts, and proper navigation
- [ ] **FAQ Section**: Verify FAQ section functionality
  - Test: Click on FAQ items to expand/collapse content
  - Expected: FAQ items expand/collapse smoothly with proper content display
- [ ] **How It Works Section**: Verify "How it works" content displays
  - Test: Check all steps and explanations are visible and properly formatted
  - Expected: All steps display with icons, descriptions, and proper styling

### 1.2 Navigation & Header
- [ ] **Logo Navigation**: Verify logo click returns to homepage
  - Test: Click logo from any page and verify navigation to homepage
  - Expected: Logo navigates to homepage and maintains user session
- [ ] **Main Search Bar**: Verify main search functionality in header
  - Test: Enter location and property type, then execute search
  - Expected: Search executes with proper parameters and navigates to results page
- [ ] **Language Switcher**: Verify language switching between EN/ID
  - Test: Switch between English and Indonesian languages
  - Expected: All content translates correctly, URLs update with locale, user preference saves
- [ ] **Currency Switcher**: Verify currency conversion functionality
  - Test: Switch between different currencies (EUR, USD, IDR)
  - Expected: All prices convert correctly, conversion rates are current, preference saves
- [ ] **Mobile Navigation**: Verify hamburger menu functionality on mobile
  - Test: Open/close mobile menu and test all navigation items
  - Expected: Menu opens/closes smoothly, all links work, proper responsive behavior
- [ ] **Sticky Header**: Verify header behavior during scrolling
  - Test: Scroll down page and verify header remains visible and functional
  - Expected: Header sticks to top, search remains accessible, no layout issues

## 2. SEARCH & FILTER FUNCTIONALITY

### 2.1 Main Search Bar
- [ ] **Location Search**: Verify location search with autocomplete
  - Test: Type location names and verify autocomplete suggestions appear
  - Expected: Relevant location suggestions display, selection updates search field
- [ ] **Property Type Selection**: Verify property type filter functionality
  - Test: Select multiple property types (villa, apartment, rooms, commercial)
  - Expected: Multiple selections allowed (max 3), visual indicators show selected types
- [ ] **Search Execution**: Verify search button functionality
  - Test: Execute search with various location and property type combinations
  - Expected: Search navigates to results page with correct parameters in URL
- [ ] **Search History**: Verify search history storage and display
  - Test: Perform multiple searches and verify history is saved and accessible
  - Expected: Last 5 searches saved, history displays with proper formatting, clickable to re-execute
- [ ] **Search Input Focus States**: Verify input focus behavior
  - Test: Focus on location and category inputs, verify visual feedback
  - Expected: Proper focus states, search bar expands/contracts appropriately

### 2.2 Advanced Filters
- [ ] **Price Range Filter**: Verify minimum and maximum price filtering
  - Test: Set various price ranges and verify results match criteria
  - Expected: Results show only properties within specified price range, currency conversion applies
- [ ] **Property Type Filter**: Verify all property type options
  - Test: Filter by each property type: villa, apartment, rooms, commercial, shops, offices, land
  - Expected: Results show only selected property types, multiple selections work correctly
- [ ] **Bedroom Count Filter**: Verify bedroom number filtering
  - Test: Filter by different bedroom counts (1, 2, 3, 4+)
  - Expected: Results show properties with exact or minimum bedroom count as specified
- [ ] **Bathroom Count Filter**: Verify bathroom number filtering
  - Test: Filter by different bathroom counts (1, 2, 3, 4+)
  - Expected: Results show properties with exact or minimum bathroom count as specified
- [ ] **Property Size Filters**: Verify land, building, and garden size filters
  - Test: Set minimum and maximum values for land, building, and garden sizes
  - Expected: Results show properties within specified size ranges, proper unit display
- [ ] **Year of Building Filter**: Verify construction year filtering
  - Test: Filter by different year ranges (new, 5 years, 10 years, older)
  - Expected: Results show properties built within specified time periods
- [ ] **Features Filter**: Verify amenity and feature filtering
  - Test: Select various features: bathtub, air conditioning, pet allowed, garden, gazebo, rooftop terrace, balcony, terrace
  - Expected: Results show properties with selected features, multiple feature selection works
- [ ] **Property Condition Filter**: Verify condition-based filtering
  - Test: Filter by property condition options (new, good, needs renovation)
  - Expected: Results show properties matching selected condition criteria
- [ ] **Furnished Status Filter**: Verify furnished/unfurnished filtering
  - Test: Filter by furnished, semi-furnished, and unfurnished options
  - Expected: Results show properties matching selected furnished status
- [ ] **Minimum Contract Duration**: Verify contract duration filtering
  - Test: Filter by different minimum contract periods (monthly, quarterly, yearly)
  - Expected: Results show properties with matching minimum contract requirements
- [ ] **View Type Filter**: Verify view preference filtering
  - Test: Filter by different view types (ocean, mountain, garden, city)
  - Expected: Results show properties with specified view types
- [ ] **Location-based Filtering**: Verify geographic area filtering
  - Test: Use map area selection and radius-based filtering
  - Expected: Results show properties within selected geographic boundaries

### 2.3 Search Results
- [ ] **Results Display Layout**: Verify search results presentation
  - Test: Check grid/list view toggle and property card information display
  - Expected: Properties display with image, title, price, location, key features, proper formatting
- [ ] **Pagination**: Verify pagination functionality
  - Test: Navigate through multiple pages of results (15 properties per page)
  - Expected: Pagination works correctly, page numbers accurate, proper loading states
- [ ] **Sorting Options**: Verify result sorting functionality
  - Test: Sort by price (low to high, high to low), date added, relevance
  - Expected: Results reorder correctly based on selected sorting criteria
- [ ] **Results Counter**: Verify result count display
  - Test: Check result count accuracy and updates with filter changes
  - Expected: Count displays correctly, updates in real-time with filter modifications
- [ ] **No Results Scenario**: Verify behavior when no properties match criteria
  - Test: Apply filters that return no results
  - Expected: "No results found" message displays with suggestions to modify filters
- [ ] **Loading States**: Verify loading indicators during search
  - Test: Observe loading states during search execution and filter changes
  - Expected: Proper loading indicators display, smooth transitions between states

## 3. PROPERTY DETAIL PAGE

### 3.1 Property Information Display
- [ ] **Property Title**: Verify property title displays correctly
  - Test: Check title formatting, length handling, and special characters
  - Expected: Title displays prominently with proper typography and no truncation issues
- [ ] **Property Description**: Verify description content and formatting
  - Test: Check full description display, HTML formatting, and expandable content
  - Expected: Description shows with proper formatting, "read more" functionality if long
- [ ] **Price Display**: Verify price in multiple currencies
  - Test: Check price display in EUR, USD, IDR with proper conversion rates
  - Expected: Price displays with correct currency symbols, proper formatting, real-time conversion
- [ ] **Basic Information**: Verify key property details
  - Test: Check bedrooms, bathrooms, land size, building size, garden size display
  - Expected: All details display with proper units, icons, and formatting
- [ ] **Features List**: Verify all property features display
  - Test: Check amenities, facilities, and special features with icons
  - Expected: Features display with appropriate icons, proper categorization, clear labels
- [ ] **Selling Points**: Verify unique selling points presentation
  - Test: Check highlighted features and property advantages
  - Expected: Selling points display prominently with visual emphasis
- [ ] **Availability Information**: Verify availability details
  - Test: Check available date, minimum/maximum duration, rental terms
  - Expected: Availability shows with clear dates, duration options, and booking constraints

### 3.2 Image Gallery & Media
- [ ] **Image Gallery**: Verify image gallery functionality
  - Test: Navigate through property images, check image quality and loading
  - Expected: Images load quickly, high quality, smooth navigation between images
- [ ] **Image Navigation**: Verify previous/next button functionality
  - Test: Use arrow buttons and keyboard navigation to browse images
  - Expected: Navigation works smoothly, proper image transitions, keyboard accessibility
- [ ] **Image Zoom**: Verify zoom functionality on images
  - Test: Click to zoom images, test zoom controls and pan functionality
  - Expected: Images zoom properly, pan controls work, zoom level indicators display
- [ ] **Fullscreen Mode**: Verify fullscreen image viewing
  - Test: Enter/exit fullscreen mode, navigate images in fullscreen
  - Expected: Fullscreen works across browsers, proper controls, easy exit
- [ ] **Lazy Loading**: Verify image lazy loading implementation
  - Test: Check image loading behavior as user scrolls through gallery
  - Expected: Images load progressively, proper loading indicators, performance optimization
- [ ] **Image Restrictions for Free Users**: Verify image access limitations
  - Test: Check if free users see only first 3 images with upgrade prompt
  - Expected: Free users see limited images, clear upgrade messaging, premium features highlighted

### 3.3 Map Functionality
- [ ] **Property Location Map**: Verify property location display on map
  - Test: Check if property location shows accurately on Google Maps
  - Expected: Correct location pin, proper map centering, accurate coordinates
- [ ] **Map Zoom Controls**: Verify zoom in/out functionality
  - Test: Use zoom controls and mouse wheel to zoom map
  - Expected: Smooth zoom functionality, proper zoom level indicators
- [ ] **Map Navigation**: Verify drag/pan functionality
  - Test: Drag map to explore surrounding area
  - Expected: Smooth panning, proper map boundaries, responsive controls
- [ ] **Zoom Restrictions for Free Users**: Verify zoom level limitations
  - Test: Check zoom restrictions for free users (max zoom level 13)
  - Expected: Zoom stops at restriction level, upgrade banner appears
- [ ] **Premium Upgrade Banner**: Verify upgrade prompt at zoom limit
  - Test: Reach zoom limit as free user and verify upgrade messaging
  - Expected: Clear upgrade prompt, subscription benefits explained, easy upgrade path
- [ ] **Membership-based Zoom Levels**: Verify different zoom levels per membership
  - Test: Free (max 13), Finder (max 14), Achiever (max 15) zoom levels
  - Expected: Zoom levels respect membership limits, proper restriction enforcement

### 3.4 Contact & Actions
- [ ] **Contact Owner Button**: Verify contact owner functionality
  - Test: Click contact owner button and verify chat initiation
  - Expected: For authenticated users with subscription: chat opens; For free users: upgrade prompt; For unauthenticated: login prompt
- [ ] **Add to Favorites**: Verify favorite functionality
  - Test: Add/remove property from favorites, check visual feedback
  - Expected: For authenticated users with subscription: favorites work; For free users: upgrade prompt; Visual state changes correctly
- [ ] **Share Property**: Verify social sharing functionality
  - Test: Use share buttons for different social platforms and direct link
  - Expected: Proper sharing URLs, correct metadata, functional share buttons
- [ ] **Save Property Action**: Verify save property functionality
  - Test: Save property for later viewing, check saved properties list
  - Expected: Property saves correctly, appears in saved list, proper authentication checks
- [ ] **Property Inquiry**: Verify inquiry form functionality
  - Test: Submit property inquiry with various message types
  - Expected: Form validates correctly, inquiry sends successfully, confirmation displayed

## 4. AUTHENTICATION & USER MANAGEMENT

### 4.1 Seeker Registration & Login
- [ ] **New User Registration**: Verify seeker account creation
  - Test: Register new seeker account with email, password, and personal details
  - Expected: Account creates successfully, confirmation email sent, user redirected to onboarding
- [ ] **Email Verification**: Verify email verification process
  - Test: Complete email verification from registration email link
  - Expected: Email verifies successfully, account activates, user can login
- [ ] **WhatsApp Verification**: Verify WhatsApp verification option
  - Test: Use WhatsApp verification during registration process
  - Expected: WhatsApp code sent, verification completes, account activates
- [ ] **Seeker Login**: Verify seeker login functionality
  - Test: Login with valid email/password combination
  - Expected: Login successful, user session created, redirected to appropriate page
- [ ] **Password Reset**: Verify password reset functionality
  - Test: Request password reset, receive email, complete reset process
  - Expected: Reset email sent, reset link works, new password saves, login works with new password
- [ ] **OTP Verification**: Verify OTP code functionality
  - Test: Complete OTP verification during sensitive operations
  - Expected: OTP code sent via email/SMS, verification works, operation completes
- [ ] **Google Social Login**: Verify Google authentication
  - Test: Login/register using Google account
  - Expected: Google OAuth flow works, account creates/links, user authenticated
- [ ] **Facebook Social Login**: Verify Facebook authentication
  - Test: Login/register using Facebook account
  - Expected: Facebook OAuth flow works, account creates/links, user authenticated
- [ ] **Two-Factor Authentication Setup**: Verify 2FA setup process
  - Test: Enable 2FA using authenticator app, verify setup completion
  - Expected: 2FA enables successfully, QR code generates, backup codes provided
- [ ] **TOTP Verification**: Verify TOTP code authentication
  - Test: Login with 2FA enabled, enter TOTP code from authenticator app
  - Expected: TOTP code validates, login completes, proper error handling for invalid codes
- [ ] **Login Session Management**: Verify session handling
  - Test: Check session persistence, timeout behavior, concurrent sessions
  - Expected: Sessions persist appropriately, timeout after inactivity, proper session cleanup

### 4.2 Seeker Profile Management
- [ ] **Profile View**: Verify profile page display
  - Test: Navigate to profile page and verify all information displays correctly
  - Expected: Personal info, subscription status, preferences, and settings display properly
- [ ] **Profile Editing**: Verify profile information updates
  - Test: Edit first name, last name, about section, citizenship, language preferences
  - Expected: Changes save successfully, validation works, updates reflect immediately
- [ ] **Profile Image Upload**: Verify profile photo functionality
  - Test: Upload new profile image, crop/resize if needed, save changes
  - Expected: Image uploads successfully, proper file type/size validation, image displays correctly
- [ ] **Personal Information Updates**: Verify contact information changes
  - Test: Update email address, phone number, address information
  - Expected: Changes require verification where appropriate, updates save correctly
- [ ] **Language Preference**: Verify language setting functionality
  - Test: Change language preference in profile settings
  - Expected: Language preference saves, interface updates, preference persists across sessions
- [ ] **Social Media Links**: Verify social media profile links
  - Test: Add/update Facebook and Twitter profile links
  - Expected: Links save correctly, validation for proper URLs, links display in profile
- [ ] **Address Management**: Verify address information handling
  - Test: Add/update address information including country, city, postal code
  - Expected: Address saves correctly, proper validation, formatting according to country standards

### 4.3 Security Settings
- [ ] **Password Change**: Verify password update functionality
  - Test: Change password using current password verification
  - Expected: Current password validates, new password meets requirements, change successful
- [ ] **Two-Factor Authentication Management**: Verify 2FA settings
  - Test: Enable/disable 2FA, regenerate backup codes, change authenticator app
  - Expected: 2FA toggles correctly, backup codes generate, proper security warnings
- [ ] **Login History**: Verify login activity tracking
  - Test: View login history with timestamps, locations, and device information
  - Expected: Login history displays accurately, suspicious activity flagged, proper date formatting
- [ ] **Active Sessions Management**: Verify session control
  - Test: View active sessions, terminate specific sessions, logout from all devices
  - Expected: Active sessions list accurately, remote logout works, current session handling
- [ ] **Account Security Overview**: Verify security status display
  - Test: Check security recommendations, account security score, pending actions
  - Expected: Security status accurate, recommendations helpful, clear action items

## 5. FAVORITES & SAVED SEARCHES

### 5.1 Favorites Management
- [ ] **Add Property to Favorites**: Verify favorite addition functionality
  - Test: Click heart icon on property cards and detail pages to add favorites
  - Expected: For subscribed users: property adds to favorites, visual feedback shows; For free users: upgrade prompt displays
- [ ] **Remove from Favorites**: Verify favorite removal functionality
  - Test: Click heart icon on favorited properties to remove from favorites
  - Expected: Property removes from favorites, visual state updates, confirmation if needed
- [ ] **Favorites List View**: Verify favorites page display
  - Test: Navigate to favorites page and verify all saved properties display
  - Expected: All favorited properties show with images, titles, prices, and key details
- [ ] **Favorites Sorting**: Verify sorting options for favorites
  - Test: Sort favorites by date added, price, location, property type
  - Expected: Favorites reorder correctly based on selected sorting criteria
- [ ] **Favorites Search**: Verify search within favorites
  - Test: Use search functionality to find specific properties in favorites list
  - Expected: Search filters favorites correctly, results update in real-time
- [ ] **Favorites Pagination**: Verify pagination for large favorites lists
  - Test: Add many properties to favorites and test pagination functionality
  - Expected: Pagination works correctly, proper page navigation, accurate counts
- [ ] **Favorites Limit Enforcement**: Verify subscription-based limits
  - Test: Free users: no favorites allowed; Finder: 20 favorites; Achiever: unlimited
  - Expected: Limits enforced correctly, upgrade prompts when limits reached

### 5.2 Saved Searches
- [ ] **Save Search Filters**: Verify search filter saving functionality
  - Test: Apply multiple filters and save the search configuration
  - Expected: Search parameters save correctly, named searches created, easy access
- [ ] **Saved Searches List**: Verify saved searches display
  - Test: View list of all saved searches with names and filter summaries
  - Expected: All saved searches display with clear names, filter summaries, creation dates
- [ ] **Execute Saved Search**: Verify saved search execution
  - Test: Click on saved search to re-run with saved parameters
  - Expected: Search executes with correct filters, results match saved criteria
- [ ] **Edit Saved Search**: Verify saved search modification
  - Test: Modify filters of existing saved search and update
  - Expected: Search updates correctly, new parameters save, old search replaced
- [ ] **Delete Saved Search**: Verify saved search removal
  - Test: Delete saved searches from the list
  - Expected: Search removes correctly, confirmation dialog if needed, list updates
- [ ] **Saved Search Notifications**: Verify alert functionality for saved searches
  - Test: Enable notifications for new properties matching saved search criteria
  - Expected: Notifications work correctly, proper frequency settings, easy unsubscribe

## 6. SUBSCRIPTION & BILLING

### 6.1 Subscription Plans
- [ ] **View Subscription Plans**: Verify subscription plan display
  - Test: View available plans (Free, Finder, Achiever) with features and pricing
  - Expected: All plans display with clear features, pricing in multiple currencies, billing options
- [ ] **Free Plan Limitations**: Verify free plan restrictions
  - Test: Verify limitations: no owner contact, limited photos (3), limited map zoom (13), no favorites
  - Expected: Restrictions enforced correctly, upgrade prompts at appropriate times
- [ ] **Finder Plan Features**: Verify Finder subscription benefits
  - Test: Verify features: 5 owner contacts/week, full photo access, zoom level 14, 20 favorites
  - Expected: All features work correctly, limits enforced, proper feature access
- [ ] **Achiever Plan Features**: Verify Achiever subscription benefits
  - Test: Verify features: 15 owner contacts/week, full access, zoom level 15, unlimited favorites
  - Expected: All premium features accessible, no restrictions, full functionality
- [ ] **Subscription Upgrade**: Verify plan upgrade process
  - Test: Upgrade from Free to Finder, Finder to Achiever
  - Expected: Upgrade process smooth, payment processing works, features activate immediately
- [ ] **Subscription Downgrade**: Verify plan downgrade process
  - Test: Downgrade from higher tier to lower tier
  - Expected: Downgrade works correctly, feature restrictions apply, proper warnings given
- [ ] **Billing Interval Selection**: Verify monthly vs quarterly billing
  - Test: Select monthly or quarterly billing options during subscription
  - Expected: Pricing adjusts correctly, billing cycle sets properly, discounts apply for quarterly
- [ ] **Subscription Cancellation**: Verify cancellation process
  - Test: Cancel active subscription and verify process
  - Expected: Cancellation works, access continues until period end, proper confirmation

### 6.2 Credit System
- [ ] **Credit Balance Display**: Verify credit balance visibility
  - Test: Check credit balance display in profile and relevant pages
  - Expected: Current credit balance displays accurately, updates in real-time
- [ ] **Credit Top-up**: Verify credit purchase functionality
  - Test: Purchase credits using various payment methods and amounts
  - Expected: Credit purchase works, payment processing secure, credits added to account
- [ ] **Credit Usage Tracking**: Verify credit consumption monitoring
  - Test: Use credits for premium features and verify deduction
  - Expected: Credits deduct correctly, usage history tracked, balance updates
- [ ] **Credit Transaction History**: Verify credit transaction records
  - Test: View history of credit purchases, usage, and refunds
  - Expected: Complete transaction history, proper categorization, accurate timestamps
- [ ] **Credit Expiration**: Verify credit validity periods
  - Test: Check if credits have expiration dates and handling
  - Expected: Expiration dates clear, warnings before expiry, proper handling of expired credits

### 6.3 Payment & Billing
- [ ] **Payment Methods**: Verify supported payment options
  - Test: Add/remove credit cards, PayPal, and other payment methods
  - Expected: Multiple payment methods supported, secure storage, easy management
- [ ] **Billing Information**: Verify billing address management
  - Test: Add/update billing address, company information, tax details
  - Expected: Billing info saves correctly, proper validation, tax calculations if applicable
- [ ] **Invoice Generation**: Verify invoice creation and access
  - Test: Generate invoices for subscriptions and credit purchases
  - Expected: Invoices generate correctly, proper formatting, downloadable PDF
- [ ] **Payment Processing**: Verify secure payment handling
  - Test: Complete payments using different methods and verify security
  - Expected: Payments process securely, proper encryption, PCI compliance
- [ ] **Billing History**: Verify billing record access
  - Test: View complete billing history with all transactions
  - Expected: Complete history available, proper categorization, search functionality
- [ ] **Auto-renewal Management**: Verify subscription auto-renewal
  - Test: Enable/disable auto-renewal for subscriptions
  - Expected: Auto-renewal toggles correctly, proper notifications, easy management

## 7. MESSAGING & COMMUNICATION

### 7.1 Chat System
- [ ] **Start Chat with Owner**: Verify owner contact functionality
  - Test: Initiate chat with property owner from property detail page
  - Expected: For subscribed users: chat opens successfully; For free users: upgrade prompt; Proper authentication checks
- [ ] **Chat Limits by Subscription**: Verify chat restrictions per plan
  - Test: Free: 0 chats, Finder: 5 chats/week, Achiever: 15 chats/week
  - Expected: Chat limits enforced correctly, counter displays accurately, reset weekly
- [ ] **Customer Support Chat**: Verify support chat functionality
  - Test: Start chat with customer support team
  - Expected: Support chat initiates correctly, proper routing, available for all users
- [ ] **Chat Interface**: Verify messaging interface functionality
  - Test: Send/receive messages, check message formatting, emoji support
  - Expected: Messages send/receive in real-time, proper formatting, emoji support works
- [ ] **Message History**: Verify chat history persistence
  - Test: Close and reopen chats, verify message history remains
  - Expected: Chat history persists correctly, proper chronological order, search functionality
- [ ] **Message Status Indicators**: Verify read/unread status
  - Test: Check message delivery and read status indicators
  - Expected: Proper status indicators (sent, delivered, read), real-time updates
- [ ] **Chat List Management**: Verify chat overview functionality
  - Test: View all active chats, sort by recent activity, search chats
  - Expected: All chats display correctly, proper sorting, search works effectively
- [ ] **Chat Notifications**: Verify notification system
  - Test: Receive notifications for new messages across different channels
  - Expected: Notifications work via email, push, in-app; proper notification settings

### 7.2 Notification Settings
- [ ] **Sound Notifications**: Verify audio notification preferences
  - Test: Enable/disable sound notifications for messages and activities
  - Expected: Sound settings save correctly, audio plays appropriately, volume controls work
- [ ] **Email Notifications**: Verify email notification preferences
  - Test: Configure email notifications for messages, property updates, price alerts
  - Expected: Email preferences save, notifications send correctly, unsubscribe options work
- [ ] **Push Notifications**: Verify browser/mobile push notifications
  - Test: Enable push notifications and verify delivery
  - Expected: Push notifications work across devices, proper permission handling, timely delivery
- [ ] **Property Alert Notifications**: Verify property update alerts
  - Test: Set up alerts for new properties matching criteria
  - Expected: Alerts trigger correctly, proper frequency settings, relevant properties only
- [ ] **Price Alert Notifications**: Verify price change alerts
  - Test: Set price alerts for specific properties or search criteria
  - Expected: Price alerts trigger on changes, proper threshold settings, accurate notifications
- [ ] **Newsletter Subscription**: Verify newsletter management
  - Test: Subscribe/unsubscribe from newsletters and marketing emails
  - Expected: Newsletter preferences save, proper opt-in/opt-out, content relevant

## 8. MAP FUNCTIONALITY

### 8.1 Search Map View
- [ ] **Map View Toggle**: Verify map/list view switching
  - Test: Toggle between map view and list view in search results
  - Expected: Views switch smoothly, data persists, proper responsive behavior
- [ ] **Property Pins on Map**: Verify property marker display
  - Test: Check if all properties show as pins on map with correct locations
  - Expected: All properties display as pins, accurate locations, proper clustering
- [ ] **Property Clustering**: Verify pin clustering functionality
  - Test: Zoom out to see property clusters, zoom in to see individual pins
  - Expected: Clustering works smoothly, proper cluster counts, smooth zoom transitions
- [ ] **Map Navigation**: Verify map interaction controls
  - Test: Pan, zoom, and navigate around the map
  - Expected: Smooth map navigation, proper boundaries, responsive controls
- [ ] **Property Pin Interaction**: Verify pin click functionality
  - Test: Click on property pins to view property information
  - Expected: Property cards display on pin click, proper information shown, easy navigation
- [ ] **Map Area Search**: Verify search within map bounds
  - Test: Move map and search within visible area
  - Expected: Search updates based on map bounds, proper area detection, results accuracy

### 8.2 Map Restrictions & Premium Features
- [ ] **Zoom Level Restrictions**: Verify membership-based zoom limits
  - Test: Free (max 13), Finder (max 14), Achiever (max 15) zoom levels
  - Expected: Zoom restrictions enforced correctly, smooth limitation handling
- [ ] **Premium Upgrade Banner**: Verify upgrade prompts on zoom limit
  - Test: Reach zoom limit and verify upgrade messaging appears
  - Expected: Clear upgrade banner, subscription benefits explained, easy upgrade path
- [ ] **Exact Location Access**: Verify precise location viewing
  - Test: Check if premium users can see exact property locations vs approximate
  - Expected: Premium users see exact locations, free users see approximate areas
- [ ] **Map Performance**: Verify map loading and performance
  - Test: Check map loading speed, responsiveness with many properties
  - Expected: Map loads quickly, smooth performance, proper optimization for large datasets

## 9. BLOG & CONTENT

### 9.1 Blog Functionality
- [ ] **Blog Article List**: Verify blog listing page
  - Test: View blog articles with proper pagination and categorization
  - Expected: Articles display with images, titles, excerpts, proper pagination
- [ ] **Blog Article Detail**: Verify individual article pages
  - Test: Read full blog articles with proper formatting and media
  - Expected: Articles display correctly, proper formatting, images load, social sharing works
- [ ] **Blog Categories**: Verify category-based filtering
  - Test: Filter blog articles by categories (real estate, market trends, etc.)
  - Expected: Category filtering works correctly, proper article categorization
- [ ] **Related Articles**: Verify related content suggestions
  - Test: Check if related articles appear at end of blog posts
  - Expected: Relevant related articles display, proper algorithm for suggestions
- [ ] **Blog Search**: Verify blog search functionality
  - Test: Search for specific topics within blog content
  - Expected: Search works effectively, relevant results, proper highlighting

## 10. RESPONSIVE DESIGN & CROSS-PLATFORM

### 10.1 Desktop Experience
- [ ] **Desktop Layout**: Verify desktop-optimized layouts
  - Test: Check all pages on desktop browsers (1920x1080, 1366x768)
  - Expected: Proper layout utilization, readable text, appropriate spacing
- [ ] **Desktop Navigation**: Verify desktop navigation functionality
  - Test: Test all navigation elements, dropdowns, and hover states
  - Expected: Smooth navigation, proper hover effects, keyboard accessibility
- [ ] **Desktop Search Interface**: Verify desktop search experience
  - Test: Use advanced filters, map view, and search functionality on desktop
  - Expected: Full functionality available, proper filter layout, efficient workflow

### 10.2 Tablet Experience
- [ ] **Tablet Layout Adaptation**: Verify tablet-responsive design
  - Test: Check layouts on iPad (768x1024) and Android tablets
  - Expected: Proper layout adaptation, touch-friendly elements, readable content
- [ ] **Tablet Touch Interface**: Verify touch interactions
  - Test: Test all touch interactions, gestures, and navigation
  - Expected: Responsive touch controls, proper gesture support, smooth interactions
- [ ] **Tablet Map Functionality**: Verify map experience on tablets
  - Test: Test map navigation, zoom, and property selection on tablets
  - Expected: Smooth map interactions, proper touch controls, good performance

### 10.3 Mobile Experience
- [ ] **Mobile Layout**: Verify mobile-optimized layouts
  - Test: Check all pages on mobile devices (375x667, 414x896)
  - Expected: Proper mobile layout, readable text, thumb-friendly navigation
- [ ] **Mobile Navigation**: Verify mobile navigation patterns
  - Test: Test hamburger menu, bottom navigation, and mobile-specific patterns
  - Expected: Intuitive mobile navigation, proper menu behavior, easy access to key features
- [ ] **Mobile Search Experience**: Verify mobile search functionality
  - Test: Use search, filters, and property browsing on mobile
  - Expected: Streamlined mobile search, easy filter access, efficient property browsing
- [ ] **Mobile Performance**: Verify mobile performance optimization
  - Test: Check loading times, image optimization, and overall performance on mobile
  - Expected: Fast loading times, optimized images, smooth scrolling and interactions

## 11. INTERNATIONALIZATION & LOCALIZATION

### 11.1 Language Support
- [ ] **English Language**: Verify English content accuracy
  - Test: Check all English content for proper grammar, spelling, and terminology
  - Expected: Professional English content, consistent terminology, proper localization
- [ ] **Indonesian Language**: Verify Indonesian content accuracy
  - Test: Check all Indonesian content for proper translation and cultural appropriateness
  - Expected: Accurate Indonesian translations, culturally appropriate content, proper formatting
- [ ] **Language Switching**: Verify language toggle functionality
  - Test: Switch between English and Indonesian languages
  - Expected: Complete language switching, URL updates, preference persistence
- [ ] **Locale-specific Formatting**: Verify regional formatting
  - Test: Check date formats, number formats, and currency display per locale
  - Expected: Proper locale formatting, appropriate date/time display, correct number separators

### 11.2 Currency & Regional Settings
- [ ] **Multi-currency Support**: Verify currency conversion functionality
  - Test: Switch between EUR, USD, and IDR currencies
  - Expected: Accurate currency conversion, real-time rates, proper currency symbols
- [ ] **Regional Content**: Verify location-specific content
  - Test: Check if content adapts based on user location/language preference
  - Expected: Relevant regional content, appropriate property suggestions, local market information
- [ ] **Time Zone Handling**: Verify time zone accuracy
  - Test: Check if times display correctly based on user location
  - Expected: Proper time zone conversion, accurate timestamps, clear time indicators

## 12. PERFORMANCE & OPTIMIZATION

### 12.1 Loading Performance
- [ ] **Page Load Times**: Verify page loading performance
  - Test: Measure load times for homepage, search results, and property details
  - Expected: Homepage < 3 seconds, search results < 2 seconds, property details < 2 seconds
- [ ] **Image Optimization**: Verify image loading and optimization
  - Test: Check image compression, lazy loading, and progressive loading
  - Expected: Optimized image sizes, proper lazy loading, progressive enhancement
- [ ] **Search Performance**: Verify search response times
  - Test: Measure search execution time with various filter combinations
  - Expected: Search results < 1 second, filter updates < 500ms, smooth user experience

### 12.2 Caching & Optimization
- [ ] **Browser Caching**: Verify caching strategy effectiveness
  - Test: Check if static assets cache properly, verify cache headers
  - Expected: Proper caching headers, efficient cache utilization, faster subsequent loads
- [ ] **API Response Caching**: Verify API response optimization
  - Test: Check if API responses cache appropriately for better performance
  - Expected: Appropriate API caching, faster data retrieval, reduced server load
- [ ] **Progressive Loading**: Verify progressive enhancement
  - Test: Check if content loads progressively for better perceived performance
  - Expected: Critical content loads first, progressive enhancement, smooth loading experience

---

## TESTING EXECUTION PRIORITIES

### High Priority (Critical Functionality)
1. **Authentication System**: Login, registration, password reset, social authentication
2. **Property Search & Filtering**: Main search functionality, advanced filters, results display
3. **Property Detail Pages**: Information display, image gallery, map functionality, contact actions
4. **Subscription Management**: Plan features, upgrade/downgrade, billing functionality
5. **Core Navigation**: Homepage, main navigation, responsive design basics

### Medium Priority (Important Features)
1. **Favorites System**: Add/remove favorites, favorites management, subscription limits
2. **Messaging System**: Owner contact, chat functionality, notification system
3. **Map Functionality**: Search map, zoom restrictions, premium features
4. **Profile Management**: Profile editing, security settings, preferences
5. **Mobile Responsiveness**: Mobile layouts, touch interactions, performance

### Low Priority (Enhancement Features)
1. **Blog Functionality**: Article reading, categorization, search
2. **Advanced Notifications**: Email preferences, push notifications, alerts
3. **Internationalization**: Language switching, currency conversion, locale formatting
4. **Performance Optimization**: Advanced caching, loading optimization
5. **Edge Cases**: Error handling, extreme scenarios, accessibility features

## TEST ENVIRONMENTS
- [ ] **Development**: Local development environment for initial testing
- [ ] **Staging**: Pre-production environment for comprehensive testing
- [ ] **Production**: Live environment for final verification and monitoring

## BROWSER & DEVICE COVERAGE
- [ ] **Chrome** (Desktop & Mobile) - Primary browser support
- [ ] **Safari** (Desktop & Mobile) - iOS and macOS support
- [ ] **Firefox** (Desktop & Mobile) - Alternative browser support
- [ ] **Edge** (Desktop) - Windows browser support
- [ ] **Samsung Internet** (Mobile) - Android alternative browser

## TEST DATA REQUIREMENTS
- [ ] **Test User Accounts**: Free, Finder, and Achiever subscription accounts
- [ ] **Test Properties**: Various property types, locations, and price ranges
- [ ] **Test Payment Data**: Sandbox payment methods for subscription testing
- [ ] **Test Communication**: Sample messages and chat scenarios
- [ ] **Test Location Data**: Various geographic locations for map testing

## SUBSCRIPTION-SPECIFIC TEST SCENARIOS

### Free User Limitations Testing
- [ ] **No Owner Contact**: Verify upgrade prompt when attempting to contact owners
- [ ] **Limited Photo Access**: Verify only first 3 photos visible with upgrade prompt
- [ ] **Map Zoom Restriction**: Verify maximum zoom level 13 with upgrade banner
- [ ] **No Favorites**: Verify upgrade prompt when attempting to add favorites
- [ ] **No Saved Searches**: Verify upgrade prompt for saved search functionality

### Finder Subscription Testing
- [ ] **5 Owner Contacts/Week**: Verify weekly limit enforcement and counter accuracy
- [ ] **Full Photo Access**: Verify all property photos accessible
- [ ] **Map Zoom Level 14**: Verify increased zoom capability
- [ ] **20 Favorites Limit**: Verify favorite limit enforcement
- [ ] **Basic Premium Features**: Verify access to premium search features

### Achiever Subscription Testing
- [ ] **15 Owner Contacts/Week**: Verify highest weekly contact limit
- [ ] **Maximum Map Zoom Level 15**: Verify highest zoom level access
- [ ] **Unlimited Favorites**: Verify no restrictions on favorite count
- [ ] **Full Premium Access**: Verify access to all premium features and functionality
- [ ] **Priority Support**: Verify enhanced customer support access
