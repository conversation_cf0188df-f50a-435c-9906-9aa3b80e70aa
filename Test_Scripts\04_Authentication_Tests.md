# Test Script 04: Authentication & User Management Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Mobile (375x667)
- **Test Accounts**: New registration, existing accounts, social accounts
- **Test URL**: https://propertyplaza.com
- **Required**: Valid email addresses, phone numbers for testing

---

## TS04-01: Seeker Registration Test

### Objective
Test new seeker account registration process with all verification methods.

### Pre-conditions
- Clear browser data
- Valid email address for testing
- Valid phone number for WhatsApp verification

### Test Steps
1. **Access Registration**:
   - Navigate to homepage
   - Click "Register" or "Sign Up" button
   - Verify registration form loads

2. **Registration Form Completion**:
   - Enter first name: "Test"
   - Enter last name: "Seeker"
   - Enter valid email address
   - Create strong password (min 8 chars, mixed case, numbers)
   - Confirm password
   - Enter phone number
   - Select country/citizenship
   - Accept terms and conditions
   - Click "Register" button

3. **Email Verification**:
   - Check for registration success message
   - Check email inbox for verification email
   - Click verification link in email
   - Verify account activation message
   - Attempt login with new credentials

4. **Profile Completion**:
   - Complete any required profile information
   - Upload profile image (optional)
   - Set language preference
   - Save profile changes

### Expected Results
- Registration form validates all fields correctly
- Verification email sent immediately
- Email verification link works and activates account
- User can login successfully after verification
- Profile setup completes without errors

### Pass/Fail Criteria
- **PASS**: Complete registration process works smoothly
- **FAIL**: Registration fails, email not sent, or verification doesn't work

---

## TS04-02: Login Functionality Test

### Objective
Test seeker login with various scenarios and validation.

### Pre-conditions
- Existing verified seeker account
- Account credentials available

### Test Steps
1. **Standard Login**:
   - Navigate to login page
   - Enter valid email address
   - Enter correct password
   - Click "Login" button
   - Verify successful login and redirect

2. **Login Validation**:
   - Test with invalid email format
   - Test with non-existent email
   - Test with correct email but wrong password
   - Test with empty fields
   - Verify appropriate error messages

3. **Remember Me Functionality**:
   - Login with "Remember Me" checked
   - Close browser and reopen
   - Verify user remains logged in
   - Test session persistence

4. **Login Security**:
   - Test account lockout after multiple failed attempts
   - Verify security delay between attempts
   - Test unlock mechanism

### Expected Results
- Valid credentials allow successful login
- Invalid attempts show appropriate error messages
- Remember me functionality works correctly
- Security measures prevent brute force attacks
- Clear feedback for all login scenarios

### Pass/Fail Criteria
- **PASS**: Login works correctly with proper validation and security
- **FAIL**: Login fails, poor error handling, or security issues

---

## TS04-03: Social Authentication Test

### Objective
Test Google and Facebook social login functionality.

### Pre-conditions
- Valid Google and Facebook accounts
- Social accounts not previously linked to Property Plaza

### Test Steps
1. **Google Authentication**:
   - Click "Login with Google" button
   - Verify redirect to Google OAuth
   - Authorize Property Plaza access
   - Verify successful login and account creation
   - Check profile information import
   - Test logout and re-login with Google

2. **Facebook Authentication**:
   - Click "Login with Facebook" button
   - Verify redirect to Facebook OAuth
   - Authorize Property Plaza access
   - Verify successful login and account creation
   - Check profile information import
   - Test logout and re-login with Facebook

3. **Account Linking**:
   - Login with email/password
   - Link Google account to existing account
   - Link Facebook account to existing account
   - Test login with either method
   - Verify account information consistency

4. **Social Login Error Handling**:
   - Test cancelled OAuth authorization
   - Test network interruption during OAuth
   - Verify appropriate error messages

### Expected Results
- Social OAuth flows work smoothly
- Account creation/linking works correctly
- Profile information imports appropriately
- Users can login with any linked method
- Good error handling for failed OAuth

### Pass/Fail Criteria
- **PASS**: Social authentication works reliably
- **FAIL**: OAuth fails, account linking issues, or poor error handling

---

## TS04-04: Password Reset Test

### Objective
Test password reset functionality and security measures.

### Pre-conditions
- Existing seeker account with known email
- Access to email account

### Test Steps
1. **Password Reset Request**:
   - Navigate to login page
   - Click "Forgot Password" link
   - Enter registered email address
   - Click "Send Reset Link" button
   - Verify confirmation message appears

2. **Reset Email Processing**:
   - Check email inbox for reset email
   - Verify email contains reset link
   - Check email sender and formatting
   - Note timestamp of email

3. **Password Reset Completion**:
   - Click reset link in email
   - Verify redirect to reset password page
   - Enter new strong password
   - Confirm new password
   - Submit password change
   - Verify success confirmation

4. **New Password Validation**:
   - Attempt login with old password (should fail)
   - Login with new password (should succeed)
   - Verify account access is normal

5. **Security Testing**:
   - Test reset link expiration (if applicable)
   - Test reset link single-use functionality
   - Test reset with non-existent email

### Expected Results
- Reset email sent promptly
- Reset link works correctly
- New password saves and works immediately
- Old password becomes invalid
- Proper security measures in place

### Pass/Fail Criteria
- **PASS**: Password reset works securely and reliably
- **FAIL**: Reset fails, security issues, or poor user experience

---

## TS04-05: Two-Factor Authentication (2FA) Test

### Objective
Test 2FA setup and authentication process.

### Pre-conditions
- Authenticated seeker account
- Authenticator app available (Google Authenticator, Authy, etc.)

### Test Steps
1. **2FA Setup**:
   - Navigate to security settings
   - Click "Enable Two-Factor Authentication"
   - Verify QR code displays
   - Scan QR code with authenticator app
   - Enter verification code from app
   - Save backup codes provided
   - Confirm 2FA activation

2. **2FA Login Test**:
   - Logout from account
   - Login with email and password
   - Verify 2FA code prompt appears
   - Enter code from authenticator app
   - Verify successful login

3. **Backup Code Test**:
   - Logout and login again
   - Use backup code instead of authenticator
   - Verify backup code works
   - Check if backup code becomes invalid after use

4. **2FA Management**:
   - Access 2FA settings while logged in
   - Generate new backup codes
   - Test disable 2FA functionality
   - Re-enable 2FA and verify setup

### Expected Results
- 2FA setup process is clear and functional
- QR code works with authenticator apps
- 2FA codes validate correctly during login
- Backup codes work as expected
- 2FA can be managed easily

### Pass/Fail Criteria
- **PASS**: 2FA works correctly with good user experience
- **FAIL**: 2FA setup fails, codes don't work, or poor implementation

---

## TS04-06: Profile Management Test

### Objective
Test seeker profile viewing and editing functionality.

### Pre-conditions
- Authenticated seeker account
- Profile images for testing upload

### Test Steps
1. **Profile Viewing**:
   - Navigate to profile page
   - Verify all profile information displays:
     - Personal information (name, email, phone)
     - Profile image
     - Subscription status
     - Account settings
     - Security settings

2. **Profile Information Editing**:
   - Click "Edit Profile" button
   - Update first name and last name
   - Update about/bio section
   - Change language preference
   - Update address information
   - Save changes and verify updates

3. **Profile Image Management**:
   - Click on profile image or "Change Photo"
   - Upload new profile image
   - Test image cropping/resizing if available
   - Save new profile image
   - Verify image displays correctly throughout site

4. **Contact Information Updates**:
   - Update phone number
   - Update email address (may require verification)
   - Add social media links
   - Save changes and verify updates

5. **Privacy Settings**:
   - Review privacy settings options
   - Update profile visibility settings
   - Configure communication preferences
   - Save privacy preferences

### Expected Results
- Profile displays all information clearly
- All editable fields can be updated successfully
- Image upload works with proper validation
- Changes save and persist across sessions
- Privacy settings function correctly

### Pass/Fail Criteria
- **PASS**: Profile management works completely and reliably
- **FAIL**: Profile updates fail, image upload issues, or data doesn't persist

---

## TS04-07: Security Settings Test

### Objective
Test account security features and session management.

### Pre-conditions
- Authenticated seeker account
- Multiple browser sessions if possible

### Test Steps
1. **Password Change**:
   - Navigate to security settings
   - Click "Change Password"
   - Enter current password
   - Enter new strong password
   - Confirm new password
   - Save password change
   - Test login with new password

2. **Login History Review**:
   - Access login history/activity
   - Verify recent login entries show:
     - Login timestamp
     - IP address or location
     - Device/browser information
     - Login method (email, Google, Facebook)

3. **Active Sessions Management**:
   - View active sessions list
   - Identify current session
   - Terminate a specific session (if multiple available)
   - Use "Logout from all devices" function
   - Verify forced logout works

4. **Security Overview**:
   - Review account security status
   - Check security recommendations
   - Verify 2FA status display
   - Review connected social accounts

### Expected Results
- Password changes work immediately
- Login history is accurate and detailed
- Session management functions correctly
- Security overview provides helpful information
- All security features are accessible

### Pass/Fail Criteria
- **PASS**: All security features work correctly
- **FAIL**: Security functions fail or provide inaccurate information

---

## Test Completion Checklist

- [ ] TS04-01: Seeker Registration Test
- [ ] TS04-02: Login Functionality Test
- [ ] TS04-03: Social Authentication Test
- [ ] TS04-04: Password Reset Test
- [ ] TS04-05: Two-Factor Authentication (2FA) Test
- [ ] TS04-06: Profile Management Test
- [ ] TS04-07: Security Settings Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 90-120 minutes
