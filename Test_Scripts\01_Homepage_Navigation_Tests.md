# Test Script 01: Homepage & Navigation Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Tablet (768x1024), Mobile (375x667)
- **User Account**: Not required for most tests
- **Test URL**: https://propertyplaza.com

---

## TS01-01: Homepage Content Loading Test

### Objective
Verify that the homepage loads correctly with all sections visible and functional.

### Pre-conditions
- Clear browser cache
- Stable internet connection

### Test Steps
1. Navigate to the homepage URL
2. Wait for page to fully load (max 5 seconds)
3. Verify the following sections are visible:
   - Header with logo and navigation
   - Main search bar
   - Popular properties section
   - Newest properties section
   - Featured properties section
   - Property categories section
   - Blog content section
   - FAQ section
   - "How it works" section
   - Footer

### Expected Results
- Page loads within 3 seconds
- All sections display correctly
- No broken images or missing content
- Proper responsive layout on all devices

### Pass/Fail Criteria
- **PASS**: All sections load and display correctly
- **FAIL**: Any section missing, broken, or takes >5 seconds to load

---

## TS01-02: Main Search Bar Functionality Test

### Objective
Test the main search functionality in the homepage header.

### Pre-conditions
- Homepage loaded successfully

### Test Steps
1. Click on the location search field
2. Type "Canggu" and verify autocomplete suggestions appear
3. Select "Canggu, Bali" from suggestions
4. Click on property type dropdown
5. Select "Villa" and "Apartment" (multiple selection)
6. Click the "Search" button
7. Verify navigation to search results page

### Expected Results
- Autocomplete suggestions appear while typing
- Multiple property types can be selected (max 3)
- Search executes and navigates to results page
- URL contains correct search parameters
- Search results match selected criteria

### Pass/Fail Criteria
- **PASS**: Search executes correctly with proper parameters
- **FAIL**: Search fails, no suggestions, or incorrect results

---

## TS01-03: Language Switching Test

### Objective
Verify language switching between English and Indonesian.

### Pre-conditions
- Homepage loaded in default language

### Test Steps
1. Locate language switcher in header
2. Note current language (EN or ID)
3. Click on language switcher
4. Select alternative language
5. Verify page content changes to selected language
6. Check URL for locale parameter
7. Navigate to different pages and verify language persistence
8. Switch back to original language

### Expected Results
- Language switcher is clearly visible
- Content translates completely to selected language
- URL updates with locale parameter (/en/ or /id/)
- Language preference persists across page navigation
- All UI elements translate correctly

### Pass/Fail Criteria
- **PASS**: Complete language switching with proper translation
- **FAIL**: Incomplete translation or language preference not persisting

---

## TS01-04: Currency Conversion Test

### Objective
Test currency switching and conversion functionality.

### Pre-conditions
- Homepage loaded with property prices visible

### Test Steps
1. Locate currency switcher in header
2. Note current currency and sample property prices
3. Switch to EUR currency
4. Verify all prices update to EUR with proper formatting
5. Switch to USD currency
6. Verify all prices update to USD
7. Switch to IDR currency
8. Verify all prices update to IDR
9. Check if conversion rates are reasonable and current

### Expected Results
- Currency switcher is accessible and functional
- All prices convert correctly with proper currency symbols
- Conversion rates appear current and reasonable
- Currency preference saves for session
- Proper number formatting for each currency

### Pass/Fail Criteria
- **PASS**: All currencies work with accurate conversion
- **FAIL**: Conversion fails or shows unrealistic rates

---

## TS01-05: Mobile Navigation Test

### Objective
Verify mobile navigation functionality including hamburger menu.

### Pre-conditions
- Access site on mobile device or mobile viewport (375px width)

### Test Steps
1. Load homepage on mobile device
2. Verify hamburger menu icon is visible in header
3. Tap hamburger menu to open navigation
4. Verify all navigation items are accessible:
   - Search
   - Properties
   - Blog
   - About
   - Contact
   - Login/Register
5. Test each navigation link
6. Verify menu closes when item is selected
7. Test menu close button/outside tap

### Expected Results
- Hamburger menu displays clearly on mobile
- Menu opens/closes smoothly with animation
- All navigation items are accessible and functional
- Menu has proper touch targets (min 44px)
- Navigation works without horizontal scrolling

### Pass/Fail Criteria
- **PASS**: Mobile navigation fully functional and user-friendly
- **FAIL**: Menu doesn't work or navigation items inaccessible

---

## TS01-06: Property Sections Display Test

### Objective
Verify that property sections (Popular, Newest, Featured) display correctly with proper data.

### Pre-conditions
- Homepage loaded successfully
- Test properties available in database

### Test Steps
1. Scroll to "Popular Properties" section
2. Verify section displays 6-8 properties
3. Check each property card contains:
   - Property image
   - Property title
   - Price in selected currency
   - Location
   - Key features (bedrooms, bathrooms, size)
   - Selling points/badges
4. Click on a property card
5. Verify navigation to property detail page
6. Return to homepage
7. Repeat for "Newest Properties" and "Featured Properties" sections

### Expected Results
- Each section displays proper number of properties
- Property cards contain all required information
- Images load correctly with proper aspect ratio
- Prices display in selected currency
- Property cards are clickable and navigate correctly
- Lazy loading works for images

### Pass/Fail Criteria
- **PASS**: All property sections display correctly with functional links
- **FAIL**: Missing information, broken images, or non-functional links

---

## TS01-07: Sticky Header Behavior Test

### Objective
Test header behavior during page scrolling.

### Pre-conditions
- Homepage loaded on desktop browser

### Test Steps
1. Note initial header position at top of page
2. Scroll down slowly through the page
3. Verify header remains visible and "sticks" to top
4. Continue scrolling to bottom of page
5. Verify header remains accessible
6. Test search functionality while header is sticky
7. Scroll back to top and verify header behavior

### Expected Results
- Header becomes sticky after scrolling past initial position
- Header remains fully functional while sticky
- Search bar remains accessible
- No layout issues or overlapping content
- Smooth transition between static and sticky states

### Pass/Fail Criteria
- **PASS**: Header sticks properly and remains functional
- **FAIL**: Header disappears, overlaps content, or loses functionality

---

## Test Completion Checklist

- [ ] TS01-01: Homepage Content Loading Test
- [ ] TS01-02: Main Search Bar Functionality Test  
- [ ] TS01-03: Language Switching Test
- [ ] TS01-04: Currency Conversion Test
- [ ] TS01-05: Mobile Navigation Test
- [ ] TS01-06: Property Sections Display Test
- [ ] TS01-07: Sticky Header Behavior Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 45-60 minutes
