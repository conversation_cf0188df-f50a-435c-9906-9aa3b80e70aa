# Test Script 02: Search & Filter Functionality Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080) recommended for advanced filters
- **User Account**: Not required for basic search, required for saved searches
- **Test URL**: https://propertyplaza.com

---

## TS02-01: Basic Search Functionality Test

### Objective
Test the main search functionality with location and property type selection.

### Pre-conditions
- Homepage loaded successfully
- Clear any existing search filters

### Test Steps
1. Navigate to homepage or search page
2. Click on location search field
3. Type "Ubud" slowly and observe autocomplete
4. Select "Ubud, Bali" from dropdown suggestions
5. Click on property type selector
6. Select "Villa" from the options
7. Click "Search" button
8. Verify search results page loads
9. Check URL contains search parameters
10. Verify results show only villas in Ubud area

### Expected Results
- Autocomplete suggestions appear while typing location
- Location selection updates search field correctly
- Property type selection shows visual confirmation
- Search executes within 2 seconds
- Results page displays with correct filters applied
- URL contains proper search parameters
- Results match selected criteria

### Pass/Fail Criteria
- **PASS**: Search works correctly with accurate results
- **FAIL**: Search fails, no results, or incorrect filtering

---

## TS02-02: Advanced Filters Test

### Objective
Test all advanced filter options and their combinations.

### Pre-conditions
- Navigate to search results page
- Ensure advanced filters panel is accessible

### Test Steps
1. Open advanced filters panel
2. **Price Range Filter**:
   - Set minimum price: €500
   - Set maximum price: €2000
   - Apply filter and verify results
3. **Bedroom Count Filter**:
   - Select "2 bedrooms"
   - Verify results update
4. **Bathroom Count Filter**:
   - Select "2+ bathrooms"
   - Verify results update
5. **Property Size Filter**:
   - Set minimum land size: 200 sqm
   - Verify results update
6. **Features Filter**:
   - Select "Swimming Pool"
   - Select "Air Conditioning"
   - Select "Garden"
   - Verify results update
7. **Property Condition Filter**:
   - Select "Good condition"
   - Verify results update
8. Clear all filters and verify reset functionality

### Expected Results
- All filters are accessible and functional
- Results update immediately when filters are applied
- Multiple filters can be combined
- Result count updates accurately
- Clear filters functionality works
- No properties outside filter criteria appear

### Pass/Fail Criteria
- **PASS**: All filters work correctly individually and in combination
- **FAIL**: Any filter fails to work or shows incorrect results

---

## TS02-03: Search Results Display Test

### Objective
Verify search results display correctly with proper information and functionality.

### Pre-conditions
- Search executed with results available
- Results page loaded successfully

### Test Steps
1. Verify search results layout:
   - Grid view displays properly
   - Property cards show all required information
2. Check each property card contains:
   - Property image (high quality, proper aspect ratio)
   - Property title
   - Price in selected currency
   - Location
   - Bedrooms and bathrooms count
   - Property size
   - Key features/amenities
3. Test list/grid view toggle
4. Verify pagination functionality:
   - Navigate to page 2
   - Navigate back to page 1
   - Test direct page number clicking
5. Test sorting options:
   - Sort by "Price: Low to High"
   - Sort by "Price: High to Low"
   - Sort by "Newest"
   - Sort by "Relevance"

### Expected Results
- Property cards display all information clearly
- Images load quickly with proper quality
- List/grid toggle works smoothly
- Pagination works correctly (15 properties per page)
- Sorting reorders results appropriately
- Result count is accurate and updates with filters

### Pass/Fail Criteria
- **PASS**: All display elements work correctly
- **FAIL**: Missing information, broken layout, or non-functional controls

---

## TS02-04: Search Performance Test

### Objective
Test search performance and loading times under various conditions.

### Pre-conditions
- Stable internet connection
- Browser developer tools available for timing

### Test Steps
1. **Initial Search Performance**:
   - Clear browser cache
   - Execute new search
   - Measure time from search click to results display
   - Record loading time
2. **Filter Application Performance**:
   - Apply single filter
   - Measure response time
   - Apply multiple filters simultaneously
   - Measure response time
3. **Large Result Set Performance**:
   - Search for broad criteria (e.g., "Bali")
   - Measure loading time for large result set
   - Test scrolling performance with many results
4. **Network Condition Testing**:
   - Test with throttled network (if possible)
   - Verify loading indicators appear
   - Test offline behavior

### Expected Results
- Initial search completes within 2 seconds
- Filter applications respond within 500ms
- Large result sets load within 3 seconds
- Loading indicators appear during processing
- Graceful handling of slow network conditions

### Pass/Fail Criteria
- **PASS**: All performance benchmarks met
- **FAIL**: Excessive loading times or poor performance

---

## TS02-05: Search History and Saved Searches Test

### Objective
Test search history functionality and saved search features.

### Pre-conditions
- User account logged in (required for saved searches)
- Previous searches performed

### Test Steps
1. **Search History Test**:
   - Perform 3-4 different searches
   - Click on search field
   - Verify recent searches appear in dropdown
   - Click on a previous search to re-execute
   - Verify search executes with saved parameters
2. **Save Search Test**:
   - Execute search with specific filters
   - Click "Save Search" button
   - Enter search name: "Test Villa Search"
   - Save the search
   - Navigate away from search page
   - Access saved searches from profile/menu
   - Execute saved search
   - Verify filters are applied correctly
3. **Manage Saved Searches**:
   - View list of saved searches
   - Edit a saved search name
   - Delete a saved search
   - Verify changes persist

### Expected Results
- Search history saves last 5 searches
- Saved searches store all filter parameters
- Saved searches can be named and organized
- Saved searches execute with correct filters
- Management functions (edit/delete) work properly

### Pass/Fail Criteria
- **PASS**: Search history and saved searches fully functional
- **FAIL**: Features don't work or data doesn't persist

---

## TS02-06: No Results Scenario Test

### Objective
Test system behavior when search criteria return no results.

### Pre-conditions
- Access to search functionality

### Test Steps
1. Execute search with very specific criteria likely to return no results:
   - Location: "Remote area"
   - Property type: "Villa"
   - Price range: €10,000 - €20,000 per month
   - Bedrooms: 10+
   - Features: Multiple rare features
2. Verify "No results found" message displays
3. Check if suggestions are provided:
   - Modify search criteria suggestions
   - Similar properties recommendations
   - Popular searches suggestions
4. Test suggestion links functionality
5. Verify search can be easily modified from no results state

### Expected Results
- Clear "No results found" message displays
- Helpful suggestions provided to modify search
- Suggestion links are functional
- Easy path to modify search criteria
- No broken layout or error messages

### Pass/Fail Criteria
- **PASS**: No results handled gracefully with helpful guidance
- **FAIL**: Poor error handling or unhelpful messaging

---

## TS02-07: Mobile Search Experience Test

### Objective
Test search functionality on mobile devices with touch interface.

### Pre-conditions
- Mobile device or mobile viewport (375px width)
- Touch interface available

### Test Steps
1. **Mobile Search Interface**:
   - Verify search bar is easily accessible
   - Test location input with mobile keyboard
   - Test property type selection with touch
2. **Mobile Filter Interface**:
   - Open filters panel on mobile
   - Verify all filters are accessible
   - Test filter application with touch
   - Verify filter panel closes properly
3. **Mobile Results Display**:
   - Verify property cards display properly
   - Test scrolling through results
   - Test property card touch interactions
4. **Mobile Performance**:
   - Test search execution speed on mobile
   - Verify smooth scrolling and interactions
   - Test with slower mobile network

### Expected Results
- Search interface optimized for mobile
- All filters accessible and usable on mobile
- Touch targets are appropriate size (min 44px)
- Smooth performance on mobile devices
- Results display properly on small screens

### Pass/Fail Criteria
- **PASS**: Full search functionality works well on mobile
- **FAIL**: Poor mobile experience or inaccessible features

---

## Test Completion Checklist

- [ ] TS02-01: Basic Search Functionality Test
- [ ] TS02-02: Advanced Filters Test
- [ ] TS02-03: Search Results Display Test
- [ ] TS02-04: Search Performance Test
- [ ] TS02-05: Search History and Saved Searches Test
- [ ] TS02-06: No Results Scenario Test
- [ ] TS02-07: Mobile Search Experience Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 60-75 minutes
