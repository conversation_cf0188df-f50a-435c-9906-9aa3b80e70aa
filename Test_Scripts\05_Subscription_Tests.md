# Test Script 05: Subscription & Billing Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Deskt<PERSON> (1920x1080) recommended for billing forms
- **Test Accounts**: Free user, Finder subscriber, Achiever subscriber
- **Payment**: Test credit cards (use sandbox/test environment)
- **Test URL**: https://propertyplaza.com

---

## TS05-01: Subscription Plans Display Test

### Objective
Verify that all subscription plans display correctly with accurate features and pricing.

### Pre-conditions
- Navigate to subscription/pricing page
- Clear browser cache for accurate pricing display

### Test Steps
1. **Plans Overview**:
   - Navigate to subscription plans page
   - Verify three plans are displayed: Free, Finder, Achiever
   - Check plan layout and visual hierarchy
   - Verify "Most Popular" or similar badges if present

2. **Free Plan Details**:
   - Verify Free plan shows:
     - Price: €0/month
     - Features: Basic property search, limited photos (3), no owner contact
     - Limitations: No favorites, map zoom max level 13, no saved searches
   - Check "Current Plan" indicator if user is on free plan

3. **Finder Plan Details**:
   - Verify Finder plan shows:
     - Monthly price: €19/month
     - Quarterly price: €49/quarter (discount applied)
     - Features: 5 owner contacts/week, full photo access, map zoom level 14
     - Features: 20 favorites, saved searches, priority support
   - Check upgrade button functionality

4. **Achiever Plan Details**:
   - Verify Achiever plan shows:
     - Monthly price: €39/month
     - Quarterly price: €99/quarter (discount applied)
     - Features: 15 owner contacts/week, unlimited favorites, map zoom level 15
     - Features: All premium features, highest priority support
   - Check upgrade button functionality

5. **Currency Conversion**:
   - Switch currency to USD and IDR
   - Verify prices convert correctly
   - Check currency symbols and formatting

### Expected Results
- All three plans display clearly with accurate information
- Pricing is correct in all currencies
- Feature lists are comprehensive and accurate
- Upgrade buttons are functional and prominent
- Visual design is professional and compelling

### Pass/Fail Criteria
- **PASS**: All plans display correctly with accurate pricing and features
- **FAIL**: Missing information, incorrect pricing, or display issues

---

## TS05-02: Free Plan Limitations Test

### Objective
Test that free plan limitations are properly enforced throughout the platform.

### Pre-conditions
- Free user account (not subscribed)
- Various properties available for testing

### Test Steps
1. **Owner Contact Limitation**:
   - Navigate to property detail page
   - Click "Contact Owner" button
   - Verify upgrade prompt appears instead of chat
   - Check upgrade messaging mentions contact limitation
   - Test upgrade button functionality

2. **Photo Access Limitation**:
   - Navigate to property with 10+ photos
   - Verify only first 3 photos are accessible
   - Try to view additional photos
   - Verify upgrade prompt appears
   - Check upgrade messaging mentions full photo access

3. **Map Zoom Limitation**:
   - Navigate to property detail page with map
   - Zoom in to maximum level
   - Verify zoom stops at level 13
   - Check upgrade banner appears
   - Verify banner mentions higher zoom levels

4. **Favorites Limitation**:
   - Try to add property to favorites
   - Verify upgrade prompt appears
   - Check messaging mentions favorites feature
   - Test upgrade button from favorites prompt

5. **Saved Searches Limitation**:
   - Perform search with filters
   - Try to save search
   - Verify upgrade prompt appears
   - Check messaging mentions saved searches feature

### Expected Results
- All limitations are enforced consistently
- Upgrade prompts appear at appropriate times
- Messaging clearly explains subscription benefits
- Upgrade buttons lead to subscription page
- No way to bypass limitations

### Pass/Fail Criteria
- **PASS**: All limitations enforced with clear upgrade messaging
- **FAIL**: Limitations can be bypassed or unclear messaging

---

## TS05-03: Subscription Upgrade Process Test

### Objective
Test the subscription upgrade process from free to paid plans.

### Pre-conditions
- Free user account logged in
- Test payment method available (sandbox environment)

### Test Steps
1. **Upgrade Initiation**:
   - Click upgrade button from limitation prompt or pricing page
   - Verify subscription selection page loads
   - Select Finder plan
   - Choose billing frequency (monthly vs quarterly)
   - Verify pricing updates based on selection

2. **Payment Information**:
   - Enter billing information:
     - Full name
     - Email address
     - Billing address
     - Country and postal code
   - Enter test credit card information
   - Verify form validation works

3. **Payment Processing**:
   - Review order summary
   - Verify pricing and billing cycle are correct
   - Click "Subscribe" or "Complete Payment"
   - Verify payment processing indicator
   - Wait for payment confirmation

4. **Subscription Activation**:
   - Verify success confirmation page
   - Check subscription status in profile
   - Test immediate feature access:
     - Try contacting property owner
     - Check photo access
     - Test map zoom levels
     - Try adding favorites

5. **Confirmation Communications**:
   - Check email for subscription confirmation
   - Verify invoice/receipt is sent
   - Check subscription details in email

### Expected Results
- Smooth upgrade process with clear steps
- Payment processing works reliably
- Features activate immediately after payment
- Confirmation email sent promptly
- User can access all subscribed features

### Pass/Fail Criteria
- **PASS**: Complete upgrade process works smoothly
- **FAIL**: Payment fails, features don't activate, or poor user experience

---

## TS05-04: Finder Subscription Features Test

### Objective
Verify that Finder subscription features work correctly and limits are enforced.

### Pre-conditions
- Finder subscriber account logged in
- Multiple properties available for testing

### Test Steps
1. **Owner Contact Feature (5/week limit)**:
   - Contact 5 different property owners within a week
   - Verify chat functionality works for each contact
   - Try to contact 6th owner
   - Verify limit enforcement message appears
   - Check counter shows remaining contacts

2. **Full Photo Access**:
   - Navigate to properties with many photos
   - Verify all photos are accessible
   - Test photo gallery navigation
   - Confirm no upgrade prompts for photos

3. **Map Zoom Level 14**:
   - Navigate to property detail pages
   - Zoom in to maximum level
   - Verify zoom reaches level 14
   - Confirm higher zoom than free users
   - Check if Achiever upgrade prompt appears

4. **Favorites Feature (20 limit)**:
   - Add properties to favorites
   - Verify favorites counter increases
   - Add 20 properties to favorites
   - Try to add 21st property
   - Verify limit enforcement

5. **Saved Searches**:
   - Create and save multiple searches
   - Verify saved searches work correctly
   - Test search execution from saved list
   - Verify no limits on saved searches

### Expected Results
- All Finder features work as specified
- Limits are enforced accurately
- Feature counters display correctly
- Good user experience within limits
- Clear messaging about limits and upgrades

### Pass/Fail Criteria
- **PASS**: All Finder features work correctly with proper limits
- **FAIL**: Features don't work, limits incorrect, or poor enforcement

---

## TS05-05: Achiever Subscription Features Test

### Objective
Test that Achiever subscription provides full access to all premium features.

### Pre-conditions
- Achiever subscriber account logged in
- Various properties and features available for testing

### Test Steps
1. **Owner Contact Feature (15/week limit)**:
   - Contact multiple property owners
   - Verify higher weekly limit (15 contacts)
   - Test chat functionality
   - Verify counter shows remaining contacts accurately

2. **Maximum Map Zoom Level 15**:
   - Navigate to property detail pages
   - Zoom in to maximum level
   - Verify zoom reaches level 15 (highest available)
   - Confirm no upgrade prompts appear
   - Test zoom functionality across different properties

3. **Unlimited Favorites**:
   - Add many properties to favorites (25+)
   - Verify no limits are enforced
   - Test favorites management
   - Verify no upgrade prompts for favorites

4. **Full Premium Access**:
   - Test all premium features are accessible
   - Verify no upgrade prompts appear anywhere
   - Test priority support access
   - Verify highest tier benefits

5. **Advanced Features**:
   - Test any Achiever-exclusive features
   - Verify advanced search capabilities
   - Test premium property insights if available
   - Check priority listing in search results

### Expected Results
- All premium features accessible without limits
- Highest zoom level available
- No upgrade prompts throughout platform
- Priority support access
- Best possible user experience

### Pass/Fail Criteria
- **PASS**: Full premium access with no limitations
- **FAIL**: Any features restricted or upgrade prompts appear

---

## TS05-06: Billing and Invoice Management Test

### Objective
Test billing functionality, invoice generation, and payment history.

### Pre-conditions
- Subscribed user account (Finder or Achiever)
- Previous billing history if available

### Test Steps
1. **Billing Information Management**:
   - Navigate to billing settings
   - Update billing address
   - Change payment method
   - Update billing email
   - Save changes and verify updates

2. **Invoice Access**:
   - Navigate to billing history
   - Verify all invoices are listed
   - Download recent invoice as PDF
   - Verify invoice contains:
     - Correct billing information
     - Accurate subscription details
     - Proper pricing and taxes
     - Payment method information

3. **Payment History**:
   - Review payment history
   - Verify all transactions are recorded
   - Check transaction details:
     - Date and amount
     - Payment method
     - Invoice number
     - Transaction status

4. **Auto-renewal Settings**:
   - Access auto-renewal settings
   - Toggle auto-renewal off
   - Verify confirmation and warnings
   - Toggle auto-renewal back on
   - Verify settings save correctly

5. **Billing Notifications**:
   - Check email notification preferences
   - Verify billing reminder settings
   - Test notification preferences save

### Expected Results
- Billing information updates correctly
- Invoices generate and download properly
- Payment history is accurate and complete
- Auto-renewal settings work correctly
- Billing notifications are configurable

### Pass/Fail Criteria
- **PASS**: All billing features work correctly
- **FAIL**: Billing issues, incorrect invoices, or settings don't save

---

## TS05-07: Subscription Cancellation Test

### Objective
Test subscription cancellation process and access continuation.

### Pre-conditions
- Active subscription (Finder or Achiever)
- Understanding that cancellation may affect account access

### Test Steps
1. **Cancellation Process**:
   - Navigate to subscription settings
   - Click "Cancel Subscription" button
   - Verify cancellation confirmation dialog
   - Review cancellation terms and conditions
   - Confirm cancellation

2. **Cancellation Confirmation**:
   - Verify cancellation success message
   - Check subscription status updates
   - Verify end date is displayed
   - Check email confirmation of cancellation

3. **Access During Notice Period**:
   - Test that features remain accessible until period end
   - Verify subscription benefits continue
   - Check that no new billing occurs
   - Test feature access consistency

4. **Post-Cancellation Access**:
   - Wait until subscription period ends (or simulate)
   - Verify account reverts to free plan
   - Test that premium features are restricted
   - Verify upgrade prompts appear again

5. **Reactivation Option**:
   - Check if reactivation is possible
   - Test reactivation process if available
   - Verify features restore immediately upon reactivation

### Expected Results
- Clear cancellation process with proper warnings
- Access continues until subscription period ends
- Smooth transition to free plan after cancellation
- Option to reactivate subscription
- Proper communication throughout process

### Pass/Fail Criteria
- **PASS**: Cancellation works correctly with proper access management
- **FAIL**: Immediate access loss, billing continues, or poor process

---

## Test Completion Checklist

- [ ] TS05-01: Subscription Plans Display Test
- [ ] TS05-02: Free Plan Limitations Test
- [ ] TS05-03: Subscription Upgrade Process Test
- [ ] TS05-04: Finder Subscription Features Test
- [ ] TS05-05: Achiever Subscription Features Test
- [ ] TS05-06: Billing and Invoice Management Test
- [ ] TS05-07: Subscription Cancellation Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 120-150 minutes
