# Test Script 07: Favorites & Saved Searches Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Mobile (375x667)
- **Test Accounts**: Free user, Finder subscriber, Achiever subscriber
- **Test Data**: Multiple properties with different characteristics
- **Test URL**: https://propertyplaza.com

---

## TS07-01: Favorites Functionality Test

### Objective
Test the favorites feature with subscription-based limitations and management.

### Pre-conditions
- Various user account types available
- Multiple properties available for favoriting

### Test Steps
1. **Unauthenticated User Favorites**:
   - Logout from any account
   - Navigate to property detail page
   - Click heart/favorite icon
   - Verify login prompt appears
   - Complete login and retry favorite action

2. **Free User Favorites Limitation**:
   - Login as free user
   - Navigate to property detail page
   - Click heart/favorite icon
   - Verify upgrade prompt appears
   - Check upgrade messaging mentions favorites feature
   - Verify no properties can be favorited

3. **Finder User Favorites (20 limit)**:
   - <PERSON>gin as Finder subscriber
   - Navigate to property detail page
   - Click heart/favorite icon
   - Verify property adds to favorites
   - Check visual feedback (filled heart icon)
   - Add multiple properties to favorites (up to 20)
   - Verify favorites counter increases
   - Try to add 21st property
   - Verify limit enforcement message appears

4. **Achiever User Favorites (unlimited)**:
   - Login as Achiever subscriber
   - Add many properties to favorites (25+)
   - Verify no limits are enforced
   - Check that all properties can be favorited
   - Verify no upgrade prompts appear

5. **Favorite Visual Indicators**:
   - Verify favorited properties show filled heart icon
   - Check favorite status persists across page reloads
   - Verify favorite indicators in search results
   - Test favorite status in property cards

### Expected Results
- Proper authentication checks for favorites
- Free users see upgrade prompts consistently
- Subscription limits enforced accurately
- Visual feedback works correctly
- Favorite status persists across sessions

### Pass/Fail Criteria
- **PASS**: Favorites work correctly with proper subscription restrictions
- **FAIL**: Limits not enforced, visual feedback issues, or functionality broken

---

## TS07-02: Favorites Management Test

### Objective
Test favorites list management, organization, and removal functionality.

### Pre-conditions
- Subscribed user account (Finder or Achiever)
- Multiple properties already added to favorites

### Test Steps
1. **Favorites List Access**:
   - Navigate to favorites page/section
   - Verify all favorited properties display
   - Check favorites list layout and organization
   - Verify property information displays correctly

2. **Favorites List Information**:
   - Verify each favorite shows:
     - Property image
     - Property title
     - Price in selected currency
     - Location
     - Key features (bedrooms, bathrooms, size)
     - Date added to favorites

3. **Remove from Favorites**:
   - Click heart icon on favorited property
   - Verify property removes from favorites
   - Check visual feedback (empty heart icon)
   - Verify property disappears from favorites list
   - Test removal from property detail page

4. **Favorites Sorting**:
   - Test sort by "Date Added" (newest first)
   - Test sort by "Date Added" (oldest first)
   - Test sort by "Price" (low to high)
   - Test sort by "Price" (high to low)
   - Verify sorting works correctly

5. **Favorites Search**:
   - Use search functionality within favorites
   - Search by property name
   - Search by location
   - Verify search results accuracy
   - Test search with no results

6. **Favorites Pagination**:
   - If many favorites, test pagination
   - Navigate through multiple pages
   - Verify page navigation works correctly
   - Check favorites count accuracy

### Expected Results
- Complete favorites list with all information
- Easy removal of favorites
- Functional sorting options
- Effective search within favorites
- Proper pagination for large lists
- Good overall management experience

### Pass/Fail Criteria
- **PASS**: Favorites management works smoothly and completely
- **FAIL**: Management features don't work or poor user experience

---

## TS07-03: Saved Searches Functionality Test

### Objective
Test saved searches feature with creation, management, and execution.

### Pre-conditions
- Subscribed user account (saved searches not available for free users)
- Search functionality working properly

### Test Steps
1. **Free User Saved Search Limitation**:
   - Login as free user
   - Perform search with filters
   - Look for "Save Search" option
   - Verify upgrade prompt appears when attempting to save
   - Check upgrade messaging mentions saved searches

2. **Create Saved Search**:
   - Login as subscribed user (Finder or Achiever)
   - Perform search with specific criteria:
     - Location: "Canggu, Bali"
     - Property type: "Villa"
     - Price range: €1000-€3000
     - Bedrooms: 2+
     - Features: Swimming pool, Garden
   - Click "Save Search" button
   - Enter search name: "Canggu Villas with Pool"
   - Save the search

3. **Saved Search Confirmation**:
   - Verify save confirmation message
   - Navigate to saved searches list
   - Verify new search appears in list
   - Check search name and criteria summary

4. **Execute Saved Search**:
   - Click on saved search from list
   - Verify search executes with saved parameters
   - Check that all filters are applied correctly
   - Verify results match saved criteria

5. **Multiple Saved Searches**:
   - Create additional saved searches with different criteria
   - Verify all searches save correctly
   - Test execution of different saved searches
   - Check search list organization

### Expected Results
- Free users see upgrade prompts for saved searches
- Subscribed users can save searches successfully
- Saved searches execute with correct parameters
- Multiple saved searches can be managed
- Clear confirmation and feedback throughout

### Pass/Fail Criteria
- **PASS**: Saved searches work correctly with proper restrictions
- **FAIL**: Searches don't save, execute incorrectly, or access issues

---

## TS07-04: Saved Searches Management Test

### Objective
Test saved searches management including editing, renaming, and deletion.

### Pre-conditions
- Subscribed user account with multiple saved searches
- Saved searches list accessible

### Test Steps
1. **Saved Searches List Display**:
   - Navigate to saved searches section
   - Verify all saved searches display
   - Check search information shows:
     - Search name
     - Search criteria summary
     - Date created
     - Last executed date

2. **Edit Saved Search**:
   - Click edit option on saved search
   - Modify search criteria (change price range)
   - Update search name
   - Save changes
   - Verify updated search works correctly

3. **Rename Saved Search**:
   - Select rename option for saved search
   - Change search name to "Updated Villa Search"
   - Save new name
   - Verify name updates in list
   - Check search still executes correctly

4. **Delete Saved Search**:
   - Click delete option on saved search
   - Verify confirmation dialog appears
   - Confirm deletion
   - Verify search removes from list
   - Check search is no longer accessible

5. **Saved Search Organization**:
   - Test sorting saved searches by name
   - Test sorting by date created
   - Test sorting by last used
   - Verify organization options work

6. **Bulk Management**:
   - If available, test selecting multiple searches
   - Test bulk delete functionality
   - Verify bulk operations work correctly

### Expected Results
- Complete saved searches list with details
- Functional editing and renaming
- Safe deletion with confirmation
- Good organization options
- Bulk management if available
- Intuitive management interface

### Pass/Fail Criteria
- **PASS**: All management features work correctly
- **FAIL**: Management functions don't work or data loss occurs

---

## TS07-05: Search Alerts and Notifications Test

### Objective
Test alert functionality for saved searches when new matching properties are found.

### Pre-conditions
- Subscribed user account with saved searches
- Ability to add new properties or simulate new listings

### Test Steps
1. **Enable Search Alerts**:
   - Access saved search settings
   - Enable alerts for saved search
   - Configure alert frequency:
     - Immediate alerts
     - Daily digest
     - Weekly summary
   - Save alert preferences

2. **Alert Trigger Test**:
   - Add new property matching saved search criteria (or simulate)
   - Verify alert notification is triggered
   - Check notification delivery method:
     - Email notification
     - In-app notification
     - Push notification (if enabled)

3. **Alert Content Verification**:
   - Check alert email content
   - Verify it includes:
     - New property details
     - Property images
     - Link to property detail page
     - Saved search name reference
   - Test property link functionality

4. **Alert Frequency Testing**:
   - Test immediate alerts (if multiple properties added)
   - Test daily digest compilation
   - Test weekly summary format
   - Verify frequency settings are respected

5. **Alert Management**:
   - Disable alerts for specific saved search
   - Re-enable alerts
   - Modify alert frequency
   - Test unsubscribe from all alerts
   - Verify alert settings persist

6. **Multiple Search Alerts**:
   - Enable alerts for multiple saved searches
   - Test alerts for different search criteria
   - Verify alerts are properly categorized
   - Check alert management for multiple searches

### Expected Results
- Easy alert setup for saved searches
- Reliable alert delivery when new properties match
- Informative alert content with functional links
- Proper frequency control
- Effective alert management
- Good handling of multiple search alerts

### Pass/Fail Criteria
- **PASS**: Search alerts work reliably with good management
- **FAIL**: Alerts don't trigger, poor content, or management issues

---

## TS07-06: Mobile Favorites and Saved Searches Test

### Objective
Test favorites and saved searches functionality on mobile devices.

### Pre-conditions
- Mobile device or mobile viewport (375px width)
- Subscribed user account with existing favorites and saved searches

### Test Steps
1. **Mobile Favorites Interface**:
   - Navigate to favorites on mobile
   - Verify mobile-optimized layout
   - Check property cards display properly
   - Test touch interactions for favorites

2. **Mobile Favorite Management**:
   - Add property to favorites on mobile
   - Remove property from favorites
   - Test heart icon touch targets
   - Verify visual feedback on mobile

3. **Mobile Favorites List**:
   - Access favorites list on mobile
   - Test scrolling through favorites
   - Verify all information displays properly
   - Test sorting options on mobile

4. **Mobile Saved Searches**:
   - Access saved searches on mobile
   - Verify mobile-friendly list display
   - Test saved search execution on mobile
   - Check search results display

5. **Mobile Search Management**:
   - Test creating saved search on mobile
   - Edit saved search on mobile
   - Delete saved search on mobile
   - Verify all management functions work

6. **Mobile Performance**:
   - Test loading speed of favorites/saved searches
   - Verify smooth scrolling and interactions
   - Check overall mobile performance
   - Test with slower mobile network

### Expected Results
- Mobile-optimized interface for favorites and saved searches
- Touch-friendly interactions
- All functionality accessible on mobile
- Good performance on mobile devices
- Consistent experience across devices

### Pass/Fail Criteria
- **PASS**: Excellent mobile experience for favorites and saved searches
- **FAIL**: Poor mobile optimization or functionality issues

---

## TS07-07: Cross-Platform Synchronization Test

### Objective
Test that favorites and saved searches synchronize across different devices and browsers.

### Pre-conditions
- User account accessible from multiple devices/browsers
- Favorites and saved searches created on one device

### Test Steps
1. **Device Synchronization**:
   - Add favorites on desktop browser
   - Login to same account on mobile device
   - Verify favorites appear on mobile
   - Add favorite on mobile
   - Check if it appears on desktop

2. **Browser Synchronization**:
   - Create saved search in Chrome
   - Login to same account in Safari/Firefox
   - Verify saved search appears in other browser
   - Execute saved search in different browser
   - Verify results are consistent

3. **Real-time Synchronization**:
   - Have account open in two browser tabs
   - Add favorite in one tab
   - Check if it appears in other tab (with/without refresh)
   - Test removal synchronization

4. **Offline/Online Synchronization**:
   - Make changes while offline (if possible)
   - Go back online
   - Verify changes synchronize correctly
   - Test conflict resolution if applicable

5. **Data Consistency**:
   - Verify favorite counts are consistent across devices
   - Check saved search criteria match exactly
   - Test alert settings synchronization
   - Verify all metadata synchronizes

### Expected Results
- Perfect synchronization across all devices
- Real-time or near real-time updates
- Consistent data across platforms
- No data loss during synchronization
- Reliable offline/online sync

### Pass/Fail Criteria
- **PASS**: Flawless synchronization across all platforms
- **FAIL**: Sync issues, data inconsistencies, or data loss

---

## Test Completion Checklist

- [ ] TS07-01: Favorites Functionality Test
- [ ] TS07-02: Favorites Management Test
- [ ] TS07-03: Saved Searches Functionality Test
- [ ] TS07-04: Saved Searches Management Test
- [ ] TS07-05: Search Alerts and Notifications Test
- [ ] TS07-06: Mobile Favorites and Saved Searches Test
- [ ] TS07-07: Cross-Platform Synchronization Test

## Notes Section
_Record any issues, observations, or additional findings here:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 90-120 minutes
