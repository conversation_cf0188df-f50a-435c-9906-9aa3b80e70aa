# Test Script 09: Internationalization & Localization Tests

## Test Environment Setup
- **Browser**: Chrome/Safari/Firefox
- **Device**: Desktop (1920x1080), Mobile (375x667)
- **Languages**: English (EN), Indonesian (ID)
- **Currencies**: EUR, USD, IDR
- **Test URL**: https://propertyplaza.com

---

## TS09-01: Language Switching Functionality Test

### Objective
Test complete language switching between English and Indonesian across all pages.

### Pre-conditions
- Homepage loaded in default language
- Clear browser cache to test fresh language detection

### Test Steps
1. **Initial Language Detection**:
   - Load homepage without language preference
   - Verify default language displays (likely English)
   - Check if browser language preference is detected
   - Verify language switcher is visible in header

2. **Language Switcher Interface**:
   - Locate language switcher (flag icons or dropdown)
   - Verify both EN and ID options are available
   - Check current language is highlighted/selected
   - Test hover states and visual feedback

3. **Switch to Indonesian**:
   - Click on Indonesian (ID) language option
   - Verify page content changes to Indonesian
   - Check URL updates with locale (/id/ or ?lang=id)
   - Verify page reload or dynamic content update

4. **Indonesian Content Verification**:
   - Check navigation menu translates to Indonesian
   - Verify property descriptions in Indonesian
   - Check form labels and buttons in Indonesian
   - Verify search placeholders in Indonesian
   - Check footer content in Indonesian

5. **Switch Back to English**:
   - Click on English (EN) language option
   - Verify content switches back to English
   - Check URL updates appropriately
   - Verify all content returns to English

6. **Language Persistence Testing**:
   - Navigate to different pages (search, property detail, profile)
   - Verify language preference persists across navigation
   - Test browser refresh maintains language choice
   - Check new browser tab/window respects language setting

### Expected Results
- Language switcher is clearly visible and functional
- Complete content translation between languages
- URL properly reflects language selection
- Language preference persists across entire session
- All UI elements translate consistently

### Pass/Fail Criteria
- **PASS**: Complete, consistent language switching with persistence
- **FAIL**: Incomplete translation, language doesn't persist, or switcher doesn't work

---

## TS09-02: Currency Conversion Functionality Test

### Objective
Test currency switching and accurate conversion across all price displays.

### Pre-conditions
- Homepage loaded with properties showing prices
- Multiple properties with different price ranges available

### Test Steps
1. **Currency Switcher Interface**:
   - Locate currency switcher in header
   - Verify EUR, USD, and IDR options are available
   - Check current currency is highlighted
   - Note initial currency and sample prices

2. **Switch to EUR Currency**:
   - Select EUR from currency switcher
   - Verify all property prices update to EUR
   - Check currency symbol (€) displays correctly
   - Verify number formatting (e.g., €1,500 or €1.500)
   - Note conversion rates used

3. **Switch to USD Currency**:
   - Select USD from currency switcher
   - Verify all prices convert to USD
   - Check currency symbol ($) displays correctly
   - Verify number formatting (e.g., $1,650)
   - Compare conversion rates for reasonableness

4. **Switch to IDR Currency**:
   - Select IDR from currency switcher
   - Verify all prices convert to IDR
   - Check currency symbol (Rp) displays correctly
   - Verify number formatting (e.g., Rp 24,750,000)
   - Check large number formatting is readable

5. **Conversion Accuracy Testing**:
   - Record prices in all three currencies for same property
   - Calculate expected conversions using current exchange rates
   - Verify conversions are within reasonable range (±5%)
   - Check if conversion rates are updated regularly

6. **Currency Persistence Testing**:
   - Navigate to different pages
   - Verify currency preference persists
   - Test browser refresh maintains currency choice
   - Check search results respect currency selection

### Expected Results
- Currency switcher is accessible and functional
- All prices convert accurately with proper symbols
- Number formatting is appropriate for each currency
- Conversion rates are current and reasonable
- Currency preference persists across session

### Pass/Fail Criteria
- **PASS**: Accurate currency conversion with proper formatting and persistence
- **FAIL**: Incorrect conversions, formatting issues, or preference doesn't persist

---

## TS09-03: Combined Language and Currency Test

### Objective
Test interaction between language and currency settings across different combinations.

### Pre-conditions
- Access to both language and currency switchers
- Properties with prices available for testing

### Test Steps
1. **English + EUR Combination**:
   - Set language to English
   - Set currency to EUR
   - Verify prices show as "€1,500" format
   - Check all text is in English
   - Navigate through multiple pages

2. **English + USD Combination**:
   - Keep language as English
   - Switch currency to USD
   - Verify prices show as "$1,650" format
   - Check language remains English
   - Test search and property detail pages

3. **English + IDR Combination**:
   - Keep language as English
   - Switch currency to IDR
   - Verify prices show as "Rp 24,750,000" format
   - Check English text with Indonesian currency

4. **Indonesian + EUR Combination**:
   - Switch language to Indonesian
   - Set currency to EUR
   - Verify Indonesian text with EUR prices
   - Check cultural appropriateness of combination

5. **Indonesian + USD Combination**:
   - Keep language as Indonesian
   - Switch currency to USD
   - Verify Indonesian text with USD prices
   - Test form submissions in this combination

6. **Indonesian + IDR Combination**:
   - Keep language as Indonesian
   - Switch currency to IDR
   - Verify this is the most natural combination
   - Test all functionality in native language/currency

7. **Settings Independence**:
   - Verify language change doesn't affect currency
   - Verify currency change doesn't affect language
   - Test both settings persist independently

### Expected Results
- All language/currency combinations work correctly
- Settings are independent of each other
- Cultural appropriateness is maintained
- Both preferences persist across navigation
- No conflicts between language and currency settings

### Pass/Fail Criteria
- **PASS**: All combinations work independently with proper persistence
- **FAIL**: Settings interfere with each other or combinations don't work

---

## TS09-04: Localized Content Quality Test

### Objective
Verify quality and completeness of Indonesian translations and localized content.

### Pre-conditions
- Website set to Indonesian language
- Access to English version for comparison

### Test Steps
1. **Navigation Translation Quality**:
   - Compare English and Indonesian navigation menus
   - Verify all menu items are translated
   - Check for grammatical correctness in Indonesian
   - Verify cultural appropriateness of translations

2. **Property Content Translation**:
   - Check property titles and descriptions
   - Verify property features are translated
   - Check location names (keep original or translate appropriately)
   - Verify property type translations (Villa, Apartment, etc.)

3. **Form and Interface Translation**:
   - Check all form labels in Indonesian
   - Verify button text translations
   - Check error messages in Indonesian
   - Verify placeholder text in search fields

4. **Legal and Policy Content**:
   - Check Terms of Service in Indonesian
   - Verify Privacy Policy translation
   - Check subscription terms in Indonesian
   - Verify legal disclaimers are appropriate

5. **Cultural Localization**:
   - Check date formats (DD/MM/YYYY vs MM/DD/YYYY)
   - Verify address formats for Indonesian properties
   - Check phone number formats
   - Verify cultural sensitivity in content

6. **Missing Translation Detection**:
   - Look for English text in Indonesian mode
   - Check for translation keys or placeholders
   - Verify no mixed language content
   - Test newly added content for translation

### Expected Results
- Complete, accurate Indonesian translations
- Culturally appropriate localized content
- No English text visible in Indonesian mode
- Proper formatting for Indonesian locale
- High-quality, professional translations

### Pass/Fail Criteria
- **PASS**: High-quality, complete translations with cultural appropriateness
- **FAIL**: Poor translations, missing content, or cultural inappropriateness

---

## TS09-05: Regional Settings and Formatting Test

### Objective
Test regional formatting for dates, numbers, and addresses based on language/currency selection.

### Pre-conditions
- Access to both English and Indonesian language settings
- Properties with dates, numbers, and addresses

### Test Steps
1. **Date Formatting**:
   - Check property listing dates in English (MM/DD/YYYY or DD/MM/YYYY)
   - Switch to Indonesian and verify date format
   - Check calendar widgets use appropriate format
   - Verify month names are translated in Indonesian

2. **Number Formatting**:
   - Check large numbers in property prices
   - Verify thousand separators (comma vs period)
   - Check decimal separators for partial numbers
   - Test number formatting in different currencies

3. **Address Formatting**:
   - Check Indonesian property addresses
   - Verify address format follows Indonesian conventions
   - Check international property addresses
   - Verify postal code formatting

4. **Phone Number Formatting**:
   - Check Indonesian phone numbers (+62 format)
   - Verify international number formatting
   - Check contact form phone number validation
   - Test phone number display consistency

5. **Time Zone Handling**:
   - Check if times are displayed in local time zones
   - Verify Indonesian properties show Indonesian time
   - Check international properties show appropriate times
   - Test time zone conversion if applicable

### Expected Results
- Proper regional formatting for all data types
- Consistent formatting within each locale
- Appropriate time zone handling
- Cultural conventions respected
- No formatting conflicts between locales

### Pass/Fail Criteria
- **PASS**: Correct regional formatting for all locales
- **FAIL**: Incorrect formatting or inconsistent regional conventions

---

## TS09-06: Search and Filter Localization Test

### Objective
Test search functionality and filters work correctly in both languages with proper localization.

### Pre-conditions
- Search functionality accessible
- Advanced filters available
- Properties with Indonesian and English content

### Test Steps
1. **Search in Indonesian**:
   - Set language to Indonesian
   - Search for "villa di Canggu"
   - Verify search works with Indonesian terms
   - Check search suggestions in Indonesian
   - Verify results display Indonesian content

2. **Search in English**:
   - Switch to English
   - Search for "villa in Canggu"
   - Verify search works with English terms
   - Check search suggestions in English
   - Compare results with Indonesian search

3. **Filter Labels Translation**:
   - Open advanced filters
   - Verify all filter labels are translated
   - Check property type filters (Villa, Apartemen, etc.)
   - Verify feature filters are translated (Kolam Renang, Taman, etc.)

4. **Location Search Localization**:
   - Test location search in Indonesian
   - Search for Indonesian location names
   - Verify location suggestions work
   - Check map integration with Indonesian locations

5. **Price Range Filters**:
   - Test price filters with different currencies
   - Verify price range sliders work correctly
   - Check price input fields accept local formatting
   - Test price filter with IDR large numbers

6. **Search Results Localization**:
   - Verify search results show localized content
   - Check property descriptions in selected language
   - Verify "No results" messages are translated
   - Check pagination text is localized

### Expected Results
- Search works effectively in both languages
- All filter options are properly translated
- Search results display appropriate localized content
- Location search handles Indonesian place names
- Price filters work with all currencies

### Pass/Fail Criteria
- **PASS**: Complete search localization with full functionality
- **FAIL**: Search doesn't work in Indonesian or poor localization

---

## TS09-07: Mobile Internationalization Test

### Objective
Test language and currency functionality on mobile devices with touch interactions.

### Pre-conditions
- Mobile device or mobile viewport (375px width)
- Access to language and currency switchers on mobile

### Test Steps
1. **Mobile Language Switcher**:
   - Locate language switcher on mobile
   - Verify it's accessible and touch-friendly
   - Test switching between languages on mobile
   - Check mobile navigation translates correctly

2. **Mobile Currency Switcher**:
   - Locate currency switcher on mobile
   - Test currency switching with touch
   - Verify prices update correctly on mobile
   - Check mobile price formatting

3. **Mobile Keyboard Input**:
   - Test Indonesian text input on mobile
   - Check mobile keyboard supports Indonesian characters
   - Test search with Indonesian terms on mobile
   - Verify autocomplete works in Indonesian

4. **Mobile Content Display**:
   - Check Indonesian content displays properly on mobile
   - Verify text wrapping and formatting
   - Test long Indonesian words don't break layout
   - Check mobile property cards in Indonesian

5. **Mobile Performance**:
   - Test language switching speed on mobile
   - Check currency conversion performance
   - Verify mobile caching respects language settings
   - Test mobile network conditions with localization

### Expected Results
- Language and currency switchers work well on mobile
- Touch interactions are responsive and accurate
- Indonesian content displays properly on mobile
- Good performance for localization features
- Mobile keyboard supports Indonesian input

### Pass/Fail Criteria
- **PASS**: Excellent mobile internationalization experience
- **FAIL**: Mobile localization issues or poor touch experience

---

## Test Completion Checklist

- [ ] TS09-01: Language Switching Functionality Test
- [ ] TS09-02: Currency Conversion Functionality Test
- [ ] TS09-03: Combined Language and Currency Test
- [ ] TS09-04: Localized Content Quality Test
- [ ] TS09-05: Regional Settings and Formatting Test
- [ ] TS09-06: Search and Filter Localization Test
- [ ] TS09-07: Mobile Internationalization Test

## Localization Quality Checklist

### Translation Quality
- [ ] All navigation elements translated
- [ ] Property content properly localized
- [ ] Form labels and buttons translated
- [ ] Error messages in correct language
- [ ] Legal content appropriately localized

### Cultural Appropriateness
- [ ] Date formats follow local conventions
- [ ] Number formatting matches regional standards
- [ ] Address formats are culturally appropriate
- [ ] Currency symbols and formatting correct
- [ ] Cultural sensitivity maintained

### Technical Implementation
- [ ] Language preference persists across sessions
- [ ] Currency conversion rates are current
- [ ] URL structure reflects language selection
- [ ] Search works in both languages
- [ ] Mobile experience is fully localized

## Notes Section
_Record any translation issues, cultural concerns, or technical problems:_

---

**Test Script Version**: 1.0  
**Last Updated**: 2025-01-27  
**Estimated Execution Time**: 90-120 minutes
